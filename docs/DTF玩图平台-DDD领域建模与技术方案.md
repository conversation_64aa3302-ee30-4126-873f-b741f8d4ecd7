---
**项目**: DTF玩图平台 - 多端多角色设计创作平台
**版本**: v1.0
**创建时间**: 2025-08
**作者**: DTF技术团队 吴哲 (matrix.zhe) 
**目标市场**: 美国为主的海外市场，支持全球化扩展
---
# DTF玩图平台 - DDD领域建模与技术方案设计

## 📋 文档概述

**项目**: DTF玩图平台 - 多端多角色设计创作平台
**版本**: v1.0
**创建时间**: 2025-08
**作者**: DTF技术团队 吴哲 (matrix.zhe)
**目标市场**: 美国为主的海外市场，支持全球化扩展

## 单点登录 融合 pod开发平台



单点登录（Single Sign-On，简称 SSO）是一种身份认证方案，它允许用户使用**一组凭证（如用户名和密码）登录一次**，就能获得访问**多个相关但独立的软件系统或应用**的权限，而无需在访问每个系统时都重新输入凭证。

简单来说，就是**一次登录，处处通行**。

## 核心思想和工作原理

1. **信任关系：** 存在一个被所有参与系统信任的中央认证服务（Identity Provider，简称 IdP）。
2. **首次登录：** 当用户第一次尝试访问一个需要登录的应用（Service Provider，简称 SP）时：
   * 应用识别到用户未登录，将其重定向到中央认证服务。
   * 用户在中央认证服务处输入用户名和密码进行认证。
   * 认证成功后，中央认证服务生成一个加密的令牌（通常是安全令牌或 Cookie），包含用户的身份信息，并将其传递给用户浏览器。
3. **后续访问：** 当用户随后尝试访问另一个受信任的应用时：
   * 该应用同样发现用户未登录，将其重定向到中央认证服务。
   * 用户的浏览器在重定向请求中携带了之前获得的令牌。
   * 中央认证服务验证令牌的有效性和真实性。
   * 验证通过后，中央认证服务告知该应用用户已通过认证。
   * 该应用信任中央认证服务的判断，允许用户访问，无需用户再次输入密码。

## 关键特点

* **一次登录：** 用户只需要在中央认证服务处进行一次身份验证。
* **多系统访问：** 登录后，可以无缝访问所有集成了该 SSO 系统的应用程序。
* **无需重复输入密码：** 在访问后续系统时，用户不需要记住或输入额外的密码。
* **集中认证：** 认证过程在中央服务完成。
* **分散授权：** 各个应用系统自己管理用户在自己系统内的权限（能做什么）。

## 常见应用场景

1. **企业内部系统：** 这是最常见的场景。员工登录一次公司网络（如通过门户网站或 VPN），即可访问邮箱、内部网站、CRM 系统、HR 系统、报销系统、文件共享系统等，无需反复登录。
2. **教育机构：** 学生或教职工登录一次校园门户，即可访问选课系统、图书馆资源、成绩系统、在线学习平台等。
3. **互联网服务：** 使用 Google、Facebook、微信、Apple ID 等账号登录其他网站或应用（“使用 Google 账号登录”）。这些大型平台充当了中央认证服务的角色。
4. **云服务套件：** 如登录 Microsoft 365 后，可以访问 Outlook、Teams、OneDrive、SharePoint 等；登录 Google Workspace 后可以访问 Gmail, Drive, Docs, Meet 等。

## 主要优势

1. **提升用户体验：** 大幅减少用户需要记忆和输入的密码次数，登录流程更顺畅便捷。
2. **提高工作效率：** 用户在不同系统间切换时无需中断工作流程去登录。
3. **简化密码管理：**
   * 用户只需管理一组（或少数几组）强密码。
   * 管理员只需在中央位置管理用户账号的生命周期（创建、禁用、删除）。
4. **增强安全性（潜在）：**
   * 减少用户因记忆困难而使用弱密码或在多个系统重复使用同一密码的风险。
   * 集中式管理便于实施更强的认证策略（如多因素认证 MFA）。
   * 用户离职或账号异常时，管理员只需在中央服务禁用账号，即可立即撤销其对所有关联系统的访问权限。
5. **降低管理成本：** 减少因密码重置请求产生的 IT 支持负担。

## 潜在缺点和挑战

1. **单点故障风险：** 如果中央认证服务宕机，所有依赖它的系统都无法登录。因此需要高可用性设计。
2. **安全攻击面集中：** 中央认证服务成为攻击者的主要目标。一旦被攻破，攻击者就能获得对所有关联系统的访问权限。因此需要极高的安全防护。
3. **实现复杂性：** 在现有系统中集成 SSO 可能需要一定的开发和配置工作，需要遵循标准协议（如 SAML, OAuth, OpenID Connect）。
4. **初始设置成本：** 可能需要部署和维护额外的认证服务器和基础设施。
5. **注销复杂性：** 用户需要从中央认证服务注销才能真正退出所有系统，有些实现可能在不同系统间的会话管理上存在复杂性。

## 总结

单点登录是一种通过集中认证来简化用户访问多个系统流程的技术。它通过“一次登录，多处访问”的机制，极大地提升了用户体验和效率，同时也有助于改善密码管理和安全性（尽管也带来了新的集中风险）。它是现代企业 IT 架构和互联网服务中身份认证管理的重要组成部分。
**已知 pod开放平台和玩图 是不同的产品线，现如今公司内部各业务线维护自己的租户用户体系**
**目前的方案是，单点登录，pod开放平台存储一份通用信息，dtf也存储一份租户用户信息，支持dtf自身的自定义扩展，实现各自业务线特性数据高内聚，低耦合减少强依赖，规避由于pod的版本迭代出现bug，穿透到dtf-tool，造成业务雪崩。**


## 🎯 1.项目背景

### 1.1 业务背景

DTF玩图平台是一个面向全球市场的多端设计创作平台，主要服务于:

- **设备制造商**: 如印象派（计划年销量1万台→10万台）、农产诺、日丰产（年目标10万台级）等设备厂商
- **C端设计师**: 个人创作者和设计爱好者
- **代理商**: 设备厂商的区域代理和分销商
- **平台官方**: DTF平台运营和管理团队

### 1.2 核心价值主张

```yaml
设备厂商价值:
  - 设备监控和数据分析
  - 品牌定制化和软件管理
  - 代理商体系管理
  - 商城和图库运营

C端用户价值:
  - 多端设计工具使用
  - AI辅助创作和图片处理
  - 云存储和跨设备同步
  - 图库资源和社区交流

平台官方价值:
  - 全平台数据监控和分析
  - 内容审核和质量管控
  - 商业化运营和收益管理
  - 合规性和安全保障

业务模式:
  - OEM白标服务: 厂商品牌定制化
  - 供应链整合: 耗材商城分账体系
  - API开放: 支持创客等客户自研前端
  - 统一账号: C端/B端/厂商/代理商一体化
```

### 1.3 技术挑战

- **多租户隔离**: 设备厂商维度的完全数据隔离
- **独立部署**: 大型设备厂商的私有化部署需求
- **全球合规**: GDPR、CCPA、SOX等法规要求
- **多端同步**: PC、移动端、Web的数据一致性
- **高并发**: 全球用户的高并发访问和处理

## 🔍 2. 竞品分析

### 2.1 XTool竞品分析

基于XTool官网(https://www.xtool.com)的调研分析:

#### XTool产品特点

```yaml
硬件产品线:
  - 激光雕刻机: D1 Pro系列、P2系列
  - 软件生态: XCS软件、移动端APP
  - 用户群体: 个人创作者、小型工作室

技术架构特点:
  - 软件本地化: 主要依赖本地软件
  - 云服务有限: 基础的云存储和同步
  - 生态封闭: 主要服务自有设备

商业模式:
  - 硬件销售为主
  - 软件免费提供
  - 耗材和配件销售
```

#### DTF相对优势

```yaml
技术优势:
  - 云原生架构: 完整的云端服务体系
  - 多厂商支持: 开放的设备厂商生态
  - AI能力: 先进的AI图片处理工具
  - 全球化: 原生支持多地区部署

商业模式优势:
  - 平台化运营: 多方共赢的生态模式
  - 订阅服务: 可持续的收入模式
  - 数据价值: 设备使用数据的商业化
  - 社区生态: 用户创作内容的价值挖掘
```

### 2.2 市场定位差异化


| 维度     | XTool                 | DTF玩图平台                                                                      |
| -------- | --------------------- | -------------------------------------------------------------------------------- |
| 定位     | 平台+生态             | 平台+生态                                                                        |
| 架构     | 云原生                | 云原生                                                                           |
| 生态     | 封闭、只支持xtool设备 | 开、多设备厂商放                                                                 |
| AI能力   | 基础                  | 先进                                                                             |
| 全球化   | 有限                  | 原生支持                                                                         |
| 商业模式 | 硬件驱动              | 板卡为核心，硬件为载体，社区平台与图库赋能，设备与耗材生态共建，持续提升客户粘性 |





| 访问模式场景                             | 最小存储量 (GB) | S3 Standard-最小场景(美元/月) | S3 IT 存储费-最小场景(美元/月) | S3 IT 监控费(美元/月) | S3 IT 总计-最小场景(美元/月) | 节省金额-最小场景(美元/月) | 最大存储量 (GB) | S3 Standard-最大场景(美元/月) | S3 IT 存储费-最大场景(美元/月) | S3 IT 监控费-最大场景(美元/月) | S3 IT 总计-最大场景(美元/月) | 节省金额-最大场景(美元/月) |
|------------------------------------------|-----------------|-------------------------------|--------------------------------|-----------------------|-----------------------------|---------------------------|-----------------|-------------------------------|--------------------------------|-------------------------------|-----------------------------|---------------------------|
| 全热存储 (100% 频繁访问层)              | 4882.81         | 112.3                         | 112.30                         | 1.25                  | 113.55                      | -1.25                     | 24414.06        | 561.52                        | 561.52                         | 1.25                          | 562.77                     | -1.25                     |
| 偏热存储 (80% 频繁访问层, 20% 低频访问层) | 4882.81         | 112.3                         | 102.05                         | 1.25                  | 103.30                      | 9.00                      | 24414.06        | 561.52                        | 510.25                         | 1.25                          | 511.50                     | 50.02                     |
| 均衡存储 (50% 频繁, 30% 低频, 20% 瞬时归档) | 4882.81         | 112.3                         | 78.37                          | 1.25                  | 79.62                       | 32.69                     | 24414.06        | 561.52                        | 391.85                         | 1.25                          | 393.10                     | 168.43                    |
| 偏冷存储 (20% 频繁, 40% 低频, 40% 瞬时归档) | 4882.81         | 112.3                         | 54.69                          | 1.25                  | 55.94                       | 56.37                     | 24414.06        | 561.52                        | 273.44                         | 1.25                          | 274.69                     | 286.84                    |

以下是针对10MB × 50万张图（最小存储）和50MB × 50万张图（最大存储）的报价对比表，采用Markdown格式：

### AWS S3 存储成本对比表（全热存储场景）

| 存储场景               | 存储类型               | 存储量 (GB) | 存储费用 (美元/月) | 监控费用 (美元/月) | 总成本 (美元/月) |
|------------------------|------------------------|-------------|-------------------|-------------------|-----------------|
| **最小存储**<br>(10MB × 50万张) | S3 Standard            | 4,882.81    | 112.30            | -                 | 112.30          |
|                        | S3 Intelligent-Tiering | 4,882.81    | 112.30            | 1.25              | 113.55          |
| **最大存储**<br>(50MB × 50万张) | S3 Standard            | 24,414.06   | 561.52            | -                 | 561.52          |
|                        | S3 Intelligent-Tiering | 24,414.06   | 561.52            | 1.25              | 562.77          |

### 关键说明：
1. **存储量计算**：
  - 最小存储：500,000张 × 10MB = 5,000,000 MB ≈ 4,882.81 GB（按1GB=1024MB计算）
  - 最大存储：500,000张 × 50MB = 25,000,000 MB ≈ 24,414.06 GB
  
2. **定价基准**：
  - 存储费率：$0.023/GB（前50TB阶梯）
  - 监控费率：$0.0025/1000对象（固定$1.25/月）

3. **成本分析**：
  - **S3 Standard**：仅存储费用，无额外费用
  - **S3 Intelligent-Tiering**：存储费用 + $1.25固定监控费
  - 最小存储场景差价：$113.55 - $112.30 = **$1.25/月**（监控费）
  - 最大存储场景差价：$562.77 - $561.52 = **$1.25/月**（监控费）

4. **推荐选择**：
  - 当数据**100%为热数据**（频繁访问）时：
    - S3 Standard 更经济（节省$1.25/月监控费）
    - 成本差异仅**0.2%-0.3%**，可忽略不计
  - 当数据**访问模式不确定**时：
    - S3 Intelligent-Tiering 可能通过自动分层获得更大节省

## 👥 3. 目标客户分析

### 3.1 主要客户群体

#### 3.1.1 设备制造商 (B端)

```yaml
客户特征:
主要客户群体:
  传统设备厂商:
    - 印象派: 年销量1万台→10万台规划
    - 农产诺: 成熟设备制造商
    - 日丰产: 年目标10万台级，千万级订单潜力
  
  新兴合作伙伴:
    - 原子科技: 消费级切割机厂商
    - 裕风采: 定制需求明确
    - 创客群体: 后端需要API接入支持，自研前端
    - 安克等: 消费级市场代表

客户痛点分析:
  苹果端支持缺失:
    - 原有系统iOS适配不足
    - 移动端操作体验差
    - 跨平台兼容性问题
  
  系统复杂度高:
    - 操作流程繁琐
    - 学习成本高
    - 部署维护困难
  
  定制化需求:
    - 品牌LOGO植入
    - 界面设计定制
    - 功能模块选择
    - 供应链整合
```

#### 3.1.2 C端设计师 (C端)

```yaml
客户特征:
  - 个人创作者、设计师、手工爱好者
  - 年龄25-45岁，中高收入
  - 技术接受度高
  - 创作分享意愿强

核心需求:
  - 易用的设计工具
  - 丰富的素材资源
  - AI辅助创作
  - 作品展示和变现

痛点分析:
  - 设计软件学习成本高
  - 素材资源分散且昂贵
  - 缺乏AI辅助工具
  - 作品变现渠道有限
```

#### 3.1.3 代理商 (B端)

```yaml
客户特征:
  - 设备厂商的区域代理
  - 本地化销售和服务能力
  - 客户关系管理需求
  - 数据分析需求

核心需求:
  - 客户管理工具
  - 销售数据分析
  - 技术支持资源
  - 品牌定制化

痛点分析:
  - 缺乏统一的客户管理平台
  - 数据获取和分析困难
  - 技术支持资源不足
  - 品牌差异化不明显
```

### 3.2 竞争对手分析

```yaml
竞争威胁:
  客户自建系统:
    - 中维立方等倾向自建
    - 平台粘性削弱风险
    - 技术门槛降低趋势
  
  应对策略:
    - 提供更优的OEM方案
    - 降低客户自建成本
    - 增强平台生态价值
    - 深化供应链整合
    - 探索3D打印软件开发，拓展自有品牌业务线

核心需求:
  - 设备数据监控和分析
  - 软件定制化和品牌化
  - 代理商管理和支持
  - 用户行为数据洞察

痛点分析:
  - 软件开发成本高
  - 数据孤岛问题严重
  - 用户粘性不足
  - 售后服务成本高
```

### 3.3 地域分布分析

#### 3.3.1 美国市场 (一期重点)

```yaml
市场特点:
  - 创客文化发达
  - 技术接受度高
  - 版权意识强
  - 隐私保护要求严格

法规要求:
  - CCPA: 加州消费者隐私法案
  - SOX: 萨班斯-奥克斯利法案
  - COPPA: 儿童在线隐私保护法

技术要求:
  - 数据本地化存储
  - 加密传输和存储
  - 审计日志完整性
  - 用户权利保障
```

#### 3.3.2 欧洲市场 (二期扩展)

```yaml
市场特点:
  - 环保意识强
  - 工艺传统深厚
  - 数据保护严格
  - 多语言需求

法规要求:
  - GDPR: 通用数据保护条例
  - ePrivacy: 电子隐私指令
  - Digital Services Act: 数字服务法

技术要求:
  - 数据主体权利
  - 数据处理合法性
  - 跨境数据传输限制
  - 数据保护影响评估
```

## 📊 4. 需求分析

### 4.1 功能需求分析

#### 4.1.1 官方账号需求

```yaml
管理后台功能:
  - 用户管理: 所有角色用户的查看、审核、管理
  - 数据监控: 交易数据、用户数据、设备数据的实时监控
  - 功能配置: AI工具开放、软件更新发布控制
  - 审核管理: 账号审核、内容审核的工作流

图库管理功能:
  - 官方素材: 官方入驻模式上传免费/收费素材
  - 内容审核: C端和设备厂上传内容的审核
  - 质量控制: 素材质量标准和分类管理
  - 版权管理: 版权保护和侵权处理

商城管理功能:
  - 商品管理: 官方商品和耗材的上架管理
  - 订单处理: 订单审核、发货、售后处理
  - 供应链: 供应商管理和库存控制
  - 价格策略: 定价策略和促销活动

社区管理功能:
  - 内容审核: 用户发布内容的审核和管理
  - 社区运营: 活动策划、用户激励、内容推荐
  - 违规处理: 违规内容和用户的处理流程
  - 数据分析: 社区活跃度和用户行为分析
```

#### 4.1.2 设备厂商需求

```yaml
设备监控功能:
  - 设备状态: 实时设备在线状态和健康度监控
  - 使用数据: 设备使用时长、频次、故障统计
  - 用户行为: C端用户的设备使用行为分析
  - 预警系统: 设备故障预警和维护提醒

软件管理功能:
  - 定制化: Logo、名称、UI皮肤的品牌定制
  - 版本管理: 软件版本的发布和更新控制
  - 功能配置: 功能模块的开启和关闭控制
  - 审核流程: 定制化内容的官方审核流程

代理管理功能:
  - 代理创建: 下级代理账号的创建和管理
  - 权限分配: 代理权限的精细化控制
  - 数据查看: 代理销售数据和用户活跃度
  - 定制支持: 代理的独立品牌和图标设置

商城功能:
  - 商品上架: 自有设备和耗材的商品管理
  - 库存管理: 商品库存的实时监控和补货
  - 订单处理: 订单的处理和物流跟踪
  - 数据分析: 销售数据和用户购买行为分析

图库功能:
  - 内容上传: 素材和图片的上传和管理
  - 定价策略: 免费和收费内容的定价设置
  - 收益管理: 图库收益的统计和结算
  - 质量控制: 上传内容的质量审核
```

#### 4.1.3 C端用户需求

```yaml
设计工具功能:
  - 多端支持: PC端(Windows/Mac)、移动端(iOS/Android)
  - 设计功能: 矢量设计、图片编辑、模板应用
  - 实时预览: 设计效果的实时预览和调整
  - 格式支持: 多种文件格式的导入和导出

云存储功能:
  - 文件同步: 多端设备间的文件自动同步
  - 版本管理: 设计文件的版本历史和恢复
  - 分享协作: 文件分享和协作编辑
  - 备份恢复: 数据备份和灾难恢复

图库功能:
  - 内容消费: 图库素材的浏览、搜索、购买
  - 内容创作: 原创作品的上传、定价、销售
  - 收益管理: 创作收益的查看和提现
  - 版权保护: 原创作品的版权保护

AI工具功能:
  - 图片处理: 图生图、图片增强、风格转换
  - 智能设计: AI辅助设计和自动排版
  - 内容生成: AI生成素材和创意建议
  - 批量处理: 批量图片处理和优化

商城功能:
  - 商品浏览: 设备、耗材的浏览和比较
  - 购买流程: 下单、支付、物流跟踪
  - 订单管理: 订单历史和售后服务
  - 评价系统: 商品评价和用户反馈

社区功能:
  - 作品展示: 设计作品的发布和展示
  - 社交互动: 点赞、评论、关注、私信
  - 教程分享: 设计教程和技巧分享
  - 资源交流: 设计素材和经验交流
```

### 4.2 非功能需求分析

#### 4.2.1 性能需求

```yaml
响应时间:
  - 页面加载: < 2秒
  - API响应: < 500ms
  - 文件上传: 支持断点续传
  - 图片处理: < 10秒

并发能力:
  - 同时在线用户: 10万+
  - 峰值QPS: 1万+
  - 文件上传并发: 1000+
  - 数据库连接池: 自动扩缩容

可用性:
  - 系统可用性: 99.9%
  - 故障恢复时间: < 5分钟
  - 数据备份: 实时备份
  - 灾难恢复: < 1小时
```

#### 4.2.2 安全需求

```yaml
数据安全:
  - 传输加密: TLS 1.3
  - 存储加密: AES-256
  - 密码安全: BCrypt + 盐值
  - 敏感数据: 字段级加密

访问控制:
  - 身份认证: JWT + OAuth2
  - 权限控制: RBAC + ABAC
  - API安全: 限流 + 防刷
  - 审计日志: 完整操作记录

合规要求:
  - GDPR合规: 数据主体权利
  - CCPA合规: 隐私权保护
  - SOX合规: 财务数据审计
  - 数据本地化: 按地区存储
```

#### 4.2.3 扩展性需求

```yaml
架构扩展:
  - 微服务架构: 服务独立部署
  - 水平扩展: 自动扩缩容
  - 多地部署: 全球CDN
  - 混合云: 公有云+私有云

业务扩展:
  - 多租户: 设备厂商隔离
  - 多语言: 国际化支持
  - 多货币: 多种支付方式
  - 多地区: 本地化适配
```

## 🏗️ 5. 需求中领域对象建模

### 5.1 DDD领域建模方法论

#### 5.1.1 领域驱动设计核心概念

```yaml
战略设计:
  - 限界上下文: 明确业务边界
  - 通用语言: 统一业务术语
  - 上下文映射: 系统间关系
  - 领域专家: 业务知识来源

战术设计:
  - 实体(Entity): 有唯一标识的对象
  - 值对象(Value Object): 无标识的不可变对象
  - 聚合(Aggregate): 数据一致性边界
  - 聚合根(Aggregate Root): 聚合的入口
  - 领域服务(Domain Service): 跨实体的业务逻辑
  - 应用服务(Application Service): 用例编排
  - 仓储(Repository): 数据访问抽象
  - 工厂(Factory): 复杂对象创建
```

#### 5.1.2 六边形架构设计

```yaml
架构层次:
  - 领域层(Domain): 核心业务逻辑
  - 应用层(Application): 用例编排
  - 基础设施层(Infrastructure): 技术实现
  - 用户界面层(User Interface): 用户交互

端口和适配器:
  - 主端口(Primary Port): 应用服务接口
  - 次端口(Secondary Port): 基础设施接口
  - 主适配器(Primary Adapter): 控制器、消息监听器
  - 次适配器(Secondary Adapter): 数据库、外部服务
```

### 5.2 核心领域识别

#### 5.2.1 限界上下文划分

```plantuml
@startuml DTF_Bounded_Context

!define CONTEXT_COLOR #E1F5FE
!define AGGREGATE_COLOR #FFF3E0
!define ENTITY_COLOR #F3E5F5

package "账户管理上下文" as AccountContext CONTEXT_COLOR {
  package "用户聚合" as UserAggregate AGGREGATE_COLOR {
    class "用户" as User ENTITY_COLOR
    class "用户档案" as UserProfile ENTITY_COLOR
    class "认证信息" as AuthInfo ENTITY_COLOR
  }
  
  package "租户聚合" as TenantAggregate AGGREGATE_COLOR {
    class "租户" as Tenant ENTITY_COLOR
    class "租户配置" as TenantConfig ENTITY_COLOR
    class "部署实例" as DeploymentInstance ENTITY_COLOR
  }
}

package "权限管理上下文" as PermissionContext CONTEXT_COLOR {
  package "角色聚合" as RoleAggregate AGGREGATE_COLOR {
    class "角色" as Role ENTITY_COLOR
    class "权限" as Permission ENTITY_COLOR
    class "角色权限" as RolePermission ENTITY_COLOR
  }
}

package "设备管理上下文" as DeviceContext CONTEXT_COLOR {
  package "设备聚合" as DeviceAggregate AGGREGATE_COLOR {
    class "设备" as Device ENTITY_COLOR
    class "设备状态" as DeviceStatus ENTITY_COLOR
    class "设备数据" as DeviceData ENTITY_COLOR
  }
}

package "内容管理上下文" as ContentContext CONTEXT_COLOR {
  package "图库聚合" as GalleryAggregate AGGREGATE_COLOR {
    class "图库作品" as GalleryItem ENTITY_COLOR
    class "作品分类" as Category ENTITY_COLOR
    class "版权信息" as Copyright ENTITY_COLOR
  }
  
  package "社区聚合" as CommunityAggregate AGGREGATE_COLOR {
    class "社区内容" as CommunityContent ENTITY_COLOR
    class "用户互动" as UserInteraction ENTITY_COLOR
    class "内容审核" as ContentModeration ENTITY_COLOR
  }
}

package "商务管理上下文" as CommerceContext CONTEXT_COLOR {
  package "商城聚合" as MallAggregate AGGREGATE_COLOR {
    class "商品" as Product ENTITY_COLOR
    class "订单" as Order ENTITY_COLOR
    class "库存" as Inventory ENTITY_COLOR
  }
  
  package "钱包聚合" as WalletAggregate AGGREGATE_COLOR {
    class "钱包账户" as WalletAccount ENTITY_COLOR
    class "交易记录" as Transaction ENTITY_COLOR
    class "积分规则" as PointRule ENTITY_COLOR
  }
}

package "AI服务上下文" as AIContext CONTEXT_COLOR {
  package "AI工具聚合" as AIToolAggregate AGGREGATE_COLOR {
    class "AI工具" as AITool ENTITY_COLOR
    class "处理任务" as ProcessingTask ENTITY_COLOR
    class "AI模型" as AIModel ENTITY_COLOR
  }
}

package "存储管理上下文" as StorageContext CONTEXT_COLOR {
  package "云存储聚合" as CloudStorageAggregate AGGREGATE_COLOR {
    class "存储空间" as StorageSpace ENTITY_COLOR
    class "文件对象" as FileObject ENTITY_COLOR
    class "同步任务" as SyncTask ENTITY_COLOR
  }
}

package "合规管理上下文" as ComplianceContext CONTEXT_COLOR {
  package "数据保护聚合" as DataProtectionAggregate AGGREGATE_COLOR {
    class "隐私政策" as PrivacyPolicy ENTITY_COLOR
    class "数据处理同意" as DataProcessingConsent ENTITY_COLOR
    class "审计日志" as AuditLog ENTITY_COLOR
  }
}

' 上下文关系
AccountContext --> PermissionContext : 用户权限
AccountContext --> DeviceContext : 设备归属
AccountContext --> ContentContext : 内容创作
AccountContext --> CommerceContext : 商务交易
AccountContext --> AIContext : AI服务使用
AccountContext --> StorageContext : 存储使用
AccountContext --> ComplianceContext : 合规要求

DeviceContext --> ContentContext : 设备创作内容
ContentContext --> CommerceContext : 内容商业化
CommerceContext --> ComplianceContext : 交易合规
AIContext --> ContentContext : AI生成内容
StorageContext --> ContentContext : 内容存储

@enduml
```

### 5.3 核心领域对象详细建模

**领域概述**: 账户管理领域是DTF玩图平台的核心基础领域，负责用户身份管理、租户隔离、认证授权等关键功能。该领域采用全球化身份标识与租户隔离相结合的设计，既支持用户在不同设备厂商间的身份统一，又确保各厂商数据的完全隔离。

**核心聚合分析**:

#### 5.3.1 账户管理领域 (Account Management Domain)

**领域概述 (Domain Overview)**: 账户管理领域是DTF玩图平台的核心基础领域，负责用户身份管理、租户隔离、认证授权等关键功能。该领域采用全球化身份标识与租户隔离相结合的设计，既支持用户在不同设备厂商间的身份统一，又确保各厂商数据的完全隔离。

**核心聚合分析 (Core Aggregate Analysis)**:

##### 用户聚合 (User Aggregate)

**聚合根: User（用户）**

- **业务含义 (Business Meaning)**: 代表系统中的一个真实用户，是所有用户相关操作的入口点
- **核心职责 (Core Responsibilities)**:
  - 用户注册和登录管理 (User Registration and Login Management)
  - 用户基本信息维护 (User Basic Information Maintenance)
  - 跨租户身份统一 (Cross-tenant Identity Unification)
  - 用户状态生命周期管理 (User Status Lifecycle Management)
- **关键属性解析 (Key Attributes Analysis)**:
  - `userId`: 租户内唯一的用户标识 (Tenant-specific User Identifier)，用于租户内部业务关联
  - `globalUserId`: 全局唯一标识 (Global Unique Identifier)，支持跨租户身份识别和数据关联
  - `email`: 用户邮箱 (User Email)，支持同一邮箱在不同租户下注册
  - `userType`: 用户类型枚举 (User Type Enum)，区分官方、设备厂商、C端用户、代理等角色
  - `status`: 用户状态 (User Status)，支持激活、停用、删除等生命周期管理

**实体: UserProfile（用户档案）**

- **业务含义 (Business Meaning)**: 用户的详细个人信息和偏好设置
- **核心职责 (Core Responsibilities)**:
  - 个人信息管理 (Personal Information Management)（昵称、头像、联系方式）
  - 隐私信息保护 (Privacy Information Protection)（真实姓名、地址加密存储）
  - 用户偏好设置 (User Preference Settings)（语言、时区、通知设置）
  - 合规性信息管理 (Compliance Information Management)（GDPR同意记录）
- **关键属性解析 (Key Attributes Analysis)**:
  - `realName`: 真实姓名 (Real Name)，采用EncryptedString加密存储，符合GDPR要求
  - `phone`: 手机号 (Phone Number)，采用HashedString哈希存储，保护隐私
  - `address`: 地址信息 (Address Information)，加密存储，支持数据本地化要求
  - `preferences`: 用户偏好设置 (User Preferences)，JSON格式存储，支持灵活扩展

**实体: AuthInfo（认证信息）**

- **业务含义 (Business Meaning)**: 用户的认证凭证和安全相关信息
- **核心职责 (Core Responsibilities)**:
  - 密码安全管理 (Password Security Management)（加密存储、强度验证）
  - 多因素认证支持 (Multi-Factor Authentication Support)（MFA、TOTP）
  - 登录安全控制 (Login Security Control)（失败次数限制、账户锁定）
  - 安全审计记录 (Security Audit Recording)（登录历史、异常检测）
- **关键属性解析 (Key Attributes Analysis)**:
  - `passwordHash`: 密码哈希值 (Password Hash)，使用BCrypt+盐值加密
  - `mfaSecret`: 多因素认证密钥 (MFA Secret)，支持TOTP算法
  - `failedAttempts`: 登录失败次数 (Failed Attempts)，用于安全防护
  - `lockedUntil`: 账户锁定时间 (Locked Until)，防止暴力破解

**值对象分析 (Value Objects Analysis)**:

- `Email`: 邮箱值对象 (Email Value Object)，包含原值和哈希值，支持隐私保护
- `UserType`: 用户类型枚举 (User Type Enum)，明确区分不同角色的权限边界
- `EncryptedString`: 加密字符串 (Encrypted String)，统一处理敏感信息的加密存储
- `HashedString`: 哈希字符串 (Hashed String)，用于不可逆的隐私信息保护

##### 租户聚合 (Tenant Aggregate)

**聚合根: Tenant（租户）**

- **业务含义 (Business Meaning)**: 代表一个设备厂商或业务实体，是数据隔离和业务定制的基本单位
- **核心职责 (Core Responsibilities)**:
  - 租户基础信息管理 (Tenant Basic Information Management)
  - 数据隔离边界定义 (Data Isolation Boundary Definition)
  - 业务功能配置管理 (Business Function Configuration Management)
  - 部署模式选择 (Deployment Mode Selection)（SaaS/私有化）
- **关键属性解析 (Key Attributes Analysis)**:
  - `tenantCode`: 租户代码 (Tenant Code)，全局唯一，用于子域名和API路由
  - `tenantType`: 租户类型 (Tenant Type)，区分官方、设备厂商、代理等不同类型
  - `region`: 所属地区 (Region)，用于数据本地化和合规性管理
  - `deploymentType`: 部署类型 (Deployment Type)，支持SaaS、私有云、混合云等模式

**实体: TenantConfig（租户配置）**

- **业务含义 (Business Meaning)**: 租户的个性化配置和功能定制
- **核心职责 (Core Responsibilities)**:
  - 品牌定制化配置 (Brand Customization Configuration)（Logo、主题色、名称）
  - 功能模块开关控制 (Feature Module Toggle Control)
  - 资源配额和限制管理 (Resource Quota and Limit Management)
  - 业务规则定制化 (Business Rule Customization)
- **关键属性解析 (Key Attributes Analysis)**:
  - `branding`: 品牌配置 (Branding Configuration)，包含Logo、颜色、字体等视觉元素
  - `features`: 功能配置 (Feature Configuration)，控制AI工具、高级功能的开启状态
  - `limits`: 资源限制 (Resource Limits)，包含存储配额、用户数量、API调用限制
  - `customization`: 定制化配置 (Customization Configuration)，支持业务流程和界面的个性化

**实体: DeploymentInstance（部署实例）**

- **业务含义 (Business Meaning)**: 租户的具体部署实例，支持多地区、多模式部署
- **核心职责 (Core Responsibilities)**:
  - 部署环境管理 (Deployment Environment Management)（云端/私有化）
  - 基础设施配置管理 (Infrastructure Configuration Management)
  - 地区合规性保证 (Regional Compliance Assurance)
  - 实例状态监控 (Instance Status Monitoring)
- **关键属性解析 (Key Attributes Analysis)**:
  - `deploymentType`: 部署类型 (Deployment Type)，支持SaaS共享、私有云、本地部署
  - `region`: 部署地区 (Deployment Region)，确保数据本地化合规
  - `domain`: 访问域名 (Access Domain)，支持自定义域名和子域名
  - `infrastructure`: 基础设施配置 (Infrastructure Configuration)，包含数据库、存储、网络配置

**值对象分析 (Value Objects Analysis)**:

- `TenantType`: 租户类型枚举 (Tenant Type Enum)，明确不同租户的业务边界
- `DeploymentType`: 部署类型枚举 (Deployment Type Enum)，支持多种部署模式
- `Region`: 地区值对象 (Region Value Object)，包含合规要求和数据驻留规则

```plantuml
@startuml DTF_Account_Domain_Model

title DTF账户管理领域模型 (DTF Account Management Domain Model)

!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define AGGREGATE_ROOT_COLOR #FFE6CC

package "用户聚合 (User Aggregate)" {
  class "User\n用户" as User AGGREGATE_ROOT_COLOR {
    + userId : UserId
    + globalUserId : GlobalUserId <<全局用户ID>>
    + email : Email <<邮箱地址>>
    + userType : UserType <<用户类型>>
    + tenantId : TenantId <<租户ID>>
    + status : UserStatus <<用户状态>>
    + verificationLevel : Integer <<验证级别>>
    + riskScore : Double <<风险评分>>
    + createdAt : DateTime <<创建时间>>
    + lastLoginAt : DateTime <<最后登录时间>>
    + deletedAt : DateTime <<删除时间>>
    --
    + register(email: Email, password: String) <<用户注册>>
    + login(password: String) : AuthToken <<用户登录>>
    + updateProfile(profile: UserProfile) <<更新档案>>
    + changePassword(oldPassword: String, newPassword: String) <<修改密码>>
    + activate() <<激活账户>>
    + deactivate() <<停用账户>>
    + delete() <<删除账户>>
    + switchTenant(tenantId: TenantId) <<切换租户>>
    + calculateRiskScore() : Double <<计算风险评分>>
  }
  
  class "UserProfile\n用户档案" as UserProfile ENTITY_COLOR {
    + profileId : ProfileId
    + userId : UserId <<用户ID>>
    + nickname : String <<昵称>>
    + avatar : String <<头像URL>>
    + realName : EncryptedString <<真实姓名>>
    + phone : HashedString <<手机号>>
    + address : EncryptedString <<地址信息>>
    + birthDate : Date <<出生日期>>
    + gender : Gender <<性别>>
    + preferences : JSON <<用户偏好>>
    + language : String <<语言偏好>>
    + timezone : String <<时区设置>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + updateBasicInfo(nickname: String, avatar: String) <<更新基本信息>>
    + updateContactInfo(phone: String, address: String) <<更新联系信息>>
    + updatePreferences(preferences: JSON) <<更新偏好设置>>
    + updateLanguage(language: String) <<更新语言>>
    + updateTimezone(timezone: String) <<更新时区>>
    + encrypt() <<加密敏感信息>>
    + decrypt() <<解密敏感信息>>
  }
  
  class "AuthInfo\n认证信息" as AuthInfo ENTITY_COLOR {
    + authId : AuthId
    + userId : UserId <<用户ID>>
    + passwordHash : String <<密码哈希>>
    + salt : String <<盐值>>
    + mfaEnabled : Boolean <<MFA启用状态>>
    + mfaSecret : String <<MFA密钥>>
    + failedAttempts : Integer <<失败次数>>
    + lockedUntil : DateTime <<锁定到期时间>>
    + lastPasswordChange : DateTime <<最后密码修改时间>>
    + passwordExpiresAt : DateTime <<密码过期时间>>
    + securityQuestions : List<SecurityQuestion> <<安全问题>>
    --
    + verifyPassword(password: String) : Boolean <<验证密码>>
    + changePassword(newPassword: String) <<修改密码>>
    + enableMFA() <<启用MFA>>
    + disableMFA() <<禁用MFA>>
    + verifyMFA(code: String) : Boolean <<验证MFA>>
    + lockAccount() <<锁定账户>>
    + unlockAccount() <<解锁账户>>
    + recordFailedAttempt() <<记录失败尝试>>
    + resetFailedAttempts() <<重置失败次数>>
  }
  
  class "Email\n邮箱" as Email VALUE_OBJECT_COLOR {
    + value : String <<邮箱原值>>
    + hash : String <<邮箱哈希>>
    + domain : String <<邮箱域名>>
    + isVerified : Boolean <<是否已验证>>
    --
    + isValid() : Boolean <<邮箱格式验证>>
    + getDomain() : String <<获取域名>>
    + hash() : String <<生成哈希>>
    + mask() : String <<掩码显示>>
  }
  
  class "UserType\n用户类型" as UserType VALUE_OBJECT_COLOR {
    + DTF_OFFICIAL : "DTF官方"
    + DTF_CONSUMER : "C端用户"
    + DEVICE_VENDOR : "设备厂商"
    + VENDOR_AGENT : "厂商代理"
    + SYSTEM_ADMIN : "系统管理员"
    --
    + getDisplayName() : String <<获取显示名称>>
    + getPermissionLevel() : Integer <<获取权限级别>>
    + canManageTenant() : Boolean <<是否可管理租户>>
    + canCreateSubAccount() : Boolean <<是否可创建子账号>>
  }
  
  class "UserStatus\n用户状态" as UserStatus VALUE_OBJECT_COLOR {
    + PENDING_ACTIVATION : "待激活"
    + ACTIVE : "活跃"
    + SUSPENDED : "暂停"
    + LOCKED : "锁定"
    + DELETED : "已删除"
    --
    + canLogin() : Boolean <<是否可登录>>
    + canTransitionTo(newStatus: UserStatus) : Boolean <<状态转换检查>>
    + getDisplayName() : String <<获取显示名称>>
  }
}

package "租户聚合 (Tenant Aggregate)" {
  class "Tenant\n租户" as Tenant AGGREGATE_ROOT_COLOR {
    + tenantId : TenantId
    + tenantCode : String <<租户代码>>
    + tenantName : String <<租户名称>>
    + tenantType : TenantType <<租户类型>>
    + region : Region <<所属地区>>
    + deploymentType : DeploymentType <<部署类型>>
    + status : TenantStatus <<租户状态>>
    + parentTenantId : TenantId <<父租户ID>>
    + contactEmail : String <<联系邮箱>>
    + contactPhone : String <<联系电话>>
    + createdAt : DateTime <<创建时间>>
    + activatedAt : DateTime <<激活时间>>
    + suspendedAt : DateTime <<暂停时间>>
    --
    + activate() <<激活租户>>
    + suspend(reason: String) <<暂停租户>>
    + updateConfig(config: TenantConfig) <<更新配置>>
    + createSubTenant(subTenantInfo: TenantInfo) : Tenant <<创建子租户>>
    + assignUser(userId: UserId) <<分配用户>>
    + removeUser(userId: UserId) <<移除用户>>
    + checkDataIsolation() : Boolean <<检查数据隔离>>
  }
  
  class "TenantConfig\n租户配置" as TenantConfig ENTITY_COLOR {
    + configId : ConfigId
    + tenantId : TenantId <<租户ID>>
    + branding : BrandingConfig <<品牌配置>>
    + features : FeatureConfig <<功能配置>>
    + limits : ResourceLimits <<资源限制>>
    + customization : CustomizationConfig <<定制化配置>>
    + securitySettings : SecurityConfig <<安全设置>>
    + integrationSettings : IntegrationConfig <<集成设置>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + updateBranding(branding: BrandingConfig) <<更新品牌>>
    + enableFeature(feature: Feature) <<启用功能>>
    + disableFeature(feature: Feature) <<禁用功能>>
    + updateLimits(limits: ResourceLimits) <<更新限制>>
    + applyCustomization(customization: CustomizationConfig) <<应用定制>>
  }
  
  class "DeploymentInstance\n部署实例" as DeploymentInstance ENTITY_COLOR {
    + instanceId : InstanceId
    + tenantId : TenantId <<租户ID>>
    + deploymentType : DeploymentType <<部署类型>>
    + region : Region <<部署地区>>
    + domain : String <<访问域名>>
    + infrastructure : InfrastructureConfig <<基础设施配置>>
    + status : InstanceStatus <<实例状态>>
    + version : String <<版本号>>
    + healthStatus : HealthStatus <<健康状态>>
    + createdAt : DateTime <<创建时间>>
    + deployedAt : DateTime <<部署时间>>
    --
    + deploy() <<部署实例>>
    + upgrade(version: String) <<升级版本>>
    + scale(resources: ResourceConfig) <<扩缩容>>
    + backup() <<备份数据>>
    + restore(backupId: String) <<恢复数据>>
    + monitor() : HealthStatus <<监控状态>>
  }
  
  class "TenantType\n租户类型" as TenantType VALUE_OBJECT_COLOR {
    + DTF_OFFICIAL : "DTF官方"
    + DEVICE_VENDOR : "设备厂商"
    + VENDOR_AGENT : "厂商代理"
    + ENTERPRISE : "企业客户"
    + INDIVIDUAL : "个人用户"
    --
    + getDisplayName() : String <<获取显示名称>>
    + getDefaultFeatures() : List<Feature> <<获取默认功能>>
    + canCreateSubTenant() : Boolean <<是否可创建子租户>>
    + getDataIsolationLevel() : IsolationLevel <<获取隔离级别>>
  }
  
  class "DeploymentType\n部署类型" as DeploymentType VALUE_OBJECT_COLOR {
    + SAAS_SHARED : "SaaS共享"
    + SAAS_DEDICATED : "SaaS专用"
    + PRIVATE_CLOUD : "私有云"
    + ON_PREMISE : "本地部署"
    + HYBRID : "混合云"
    --
    + getDisplayName() : String <<获取显示名称>>
    + getIsolationLevel() : IsolationLevel <<获取隔离级别>>
    + getComplianceLevel() : ComplianceLevel <<获取合规级别>>
    + getSupportedRegions() : List<Region> <<获取支持地区>>
  }
  
  class "Region\n地区" as Region VALUE_OBJECT_COLOR {
    + code : String <<地区代码>>
    + name : String <<地区名称>>
    + country : String <<国家>>
    + dataResidencyRules : List<DataRule> <<数据驻留规则>>
    + complianceRequirements : List<ComplianceRule> <<合规要求>>
    + timezone : String <<时区>>
    --
    + getDisplayName() : String <<获取显示名称>>
    + getDataLaws() : List<DataProtectionLaw> <<获取数据保护法>>
    + isGDPRApplicable() : Boolean <<是否适用GDPR>>
    + isCCPAApplicable() : Boolean <<是否适用CCPA>>
  }
}

' 关系定义 (Relationships)
User ||--|| UserProfile : "has profile\n拥有档案"
User ||--|| AuthInfo : "has auth info\n拥有认证信息"
User ||--|| Email : "has email\n拥有邮箱"
User ||--|| UserType : "has type\n拥有类型"
User ||--|| UserStatus : "has status\n拥有状态"
Tenant ||--|| TenantConfig : "has config\n拥有配置"
Tenant ||--o{ DeploymentInstance : "has instances\n拥有实例"
Tenant ||--|| TenantType : "has type\n拥有类型"
Tenant ||--|| Region : "located in\n位于"
DeploymentInstance ||--|| DeploymentType : "has type\n拥有类型"
DeploymentInstance ||--|| Region : "deployed in\n部署于"
User }o--|| Tenant : "belongs to\n属于"

@enduml
```

#### 5.3.2 权限管理领域 (Permission Management Domain)

**领域概述 (Domain Overview)**: 权限管理领域负责DTF玩图平台的访问控制、角色管理、权限分配等核心安全功能。该领域采用基于角色的访问控制(RBAC)模型，结合租户隔离和多层级权限继承，确保不同用户类型在各自权限范围内安全地访问系统资源。

**核心聚合分析 (Core Aggregate Analysis)**:

##### 权限聚合 (Permission Aggregate)

**聚合根: Role（角色）**

- **业务含义 (Business Meaning)**: 权限的集合体，代表用户在系统中的职能和权限范围
- **核心职责 (Core Responsibilities)**:
  - 权限集合管理和组织 (Permission Set Management and Organization)
  - 用户角色分配和回收 (User Role Assignment and Revocation)
  - 角色层级关系维护 (Role Hierarchy Relationship Maintenance)
  - 租户级权限隔离 (Tenant-level Permission Isolation)
- **关键属性解析 (Key Attributes Analysis)**:
  - `roleType`: 角色类型 (Role Type)，区分系统管理员、租户管理员、普通用户等
  - `tenantId`: 所属租户 (Tenant ID)，实现租户级权限隔离
  - `permissions`: 权限列表 (Permission List)，定义角色具体的操作权限
  - `status`: 角色状态 (Role Status)，支持角色的启用、禁用管理

**实体: Permission（权限）**

- **业务含义 (Business Meaning)**: 系统中最小的权限单元，定义对特定资源的特定操作权限
- **核心职责 (Core Responsibilities)**:
  - 资源访问权限定义 (Resource Access Permission Definition)
  - 操作类型权限控制 (Operation Type Permission Control)
  - 权限范围界定 (Permission Scope Definition)
  - 条件化权限验证 (Conditional Permission Verification)
- **关键属性解析 (Key Attributes Analysis)**:
  - `resource`: 资源类型 (Resource Type)，如用户、设备、内容、订单等
  - `action`: 操作类型 (Action Type)，如创建、读取、更新、删除等
  - `scope`: 权限范围 (Permission Scope)，如全局、租户、用户级别
  - `conditions`: 权限条件 (Permission Conditions)，支持基于时间、地理位置等条件的权限控制

**实体: UserRoleAssignment（用户角色分配）**

- **业务含义 (Business Meaning)**: 用户与角色的关联关系，支持临时授权和权限继承
- **核心职责 (Core Responsibilities)**:
  - 用户角色分配管理 (User Role Assignment Management)
  - 权限有效期控制 (Permission Validity Period Control)
  - 权限分配审计 (Permission Assignment Audit)
  - 动态权限调整 (Dynamic Permission Adjustment)
- **关键属性解析 (Key Attributes Analysis)**:
  - `assignedBy`: 分配者 (Assigned By)，记录权限分配的责任人
  - `expiresAt`: 过期时间 (Expires At)，支持临时权限和定期权限审查
  - `status`: 分配状态 (Assignment Status)，支持权限的激活、暂停、撤销

**值对象分析 (Value Objects Analysis)**:

- `Resource`: 资源类型枚举 (Resource Type Enum)，标准化系统资源类型
- `Action`: 操作类型枚举 (Action Type Enum)，定义标准操作类型
- `PermissionScope`: 权限范围枚举 (Permission Scope Enum)，明确权限作用范围
- `RoleType`: 角色类型枚举 (Role Type Enum)，区分不同层级的角色

```plantuml
@startuml DTF_Permission_Domain_Model

title DTF权限管理领域模型 (DTF Permission Management Domain Model)

!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define AGGREGATE_ROOT_COLOR #FFE6CC

package "权限聚合 (Permission Aggregate)" {
  class "Role\n角色" as Role AGGREGATE_ROOT_COLOR {
    + roleId : RoleId
    + roleName : String <<角色名称>>
    + roleCode : String <<角色代码>>
    + roleType : RoleType <<角色类型>>
    + description : String <<角色描述>>
    + tenantId : TenantId <<租户ID>>
    + permissions : Set<Permission> <<权限集合>>
    + parentRoleId : RoleId <<父角色ID>>
    + status : RoleStatus <<角色状态>>
    + isSystemRole : Boolean <<是否系统角色>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + addPermission(permission: Permission) <<添加权限>>
    + removePermission(permission: Permission) <<移除权限>>
    + assignToUser(userId: UserId) : UserRoleAssignment <<分配给用户>>
    + revokeFromUser(userId: UserId) <<从用户撤销>>
    + clone(newName: String) : Role <<克隆角色>>
    + activate() <<激活角色>>
    + deactivate() <<停用角色>>
    + inheritFrom(parentRole: Role) <<继承权限>>
    + checkPermission(resource: Resource, action: Action) : Boolean <<检查权限>>
  }
  
  class "Permission\n权限" as Permission ENTITY_COLOR {
    + permissionId : PermissionId
    + permissionName : String <<权限名称>>
    + permissionCode : String <<权限代码>>
    + resource : Resource <<资源类型>>
    + action : Action <<操作类型>>
    + scope : PermissionScope <<权限范围>>
    + conditions : List<PermissionCondition> <<权限条件>>
    + description : String <<权限描述>>
    + isSystemPermission : Boolean <<是否系统权限>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + checkAccess(context: AccessContext) : AccessResult <<检查访问权限>>
    + addCondition(condition: PermissionCondition) <<添加条件>>
    + removeCondition(condition: PermissionCondition) <<移除条件>>
    + evaluateConditions(context: AccessContext) : Boolean <<评估条件>>
    + isApplicableTo(resource: Resource, action: Action) : Boolean <<是否适用>>
  }
  
  class "UserRoleAssignment\n用户角色分配" as UserRoleAssignment ENTITY_COLOR {
    + assignmentId : AssignmentId
    + userId : UserId <<用户ID>>
    + roleId : RoleId <<角色ID>>
    + tenantId : TenantId <<租户ID>>
    + assignedBy : UserId <<分配者>>
    + assignedAt : DateTime <<分配时间>>
    + expiresAt : DateTime <<过期时间>>
    + status : AssignmentStatus <<分配状态>>
    + reason : String <<分配原因>>
    + revokedAt : DateTime <<撤销时间>>
    + revokedBy : UserId <<撤销者>>
    --
    + assign() <<分配角色>>
    + revoke() <<撤销角色>>
    + extend(newExpiryDate: DateTime) <<延期>>
    + suspend() <<暂停>>
    + resume() <<恢复>>
    + isActive() : Boolean <<是否激活>>
    + isExpired() : Boolean <<是否过期>>
    + canRevoke(operatorId: UserId) : Boolean <<是否可撤销>>
  }
  
  class "PermissionCondition\n权限条件" as PermissionCondition ENTITY_COLOR {
    + conditionId : ConditionId
    + permissionId : PermissionId <<权限ID>>
    + conditionType : ConditionType <<条件类型>>
    + conditionValue : String <<条件值>>
    + operator : ConditionOperator <<操作符>>
    + description : String <<条件描述>>
    + isActive : Boolean <<是否激活>>
    + createdAt : DateTime <<创建时间>>
    --
    + evaluate(context: AccessContext) : Boolean <<评估条件>>
    + updateValue(value: String) <<更新条件值>>
    + activate() <<激活条件>>
    + deactivate() <<停用条件>>
  }
  
  class "Resource\n资源类型" as Resource VALUE_OBJECT_COLOR {
    + USER : "用户管理"
    + DEVICE : "设备管理"
    + CONTENT : "内容管理"
    + ORDER : "订单管理"
    + WALLET : "钱包管理"
    + AI_TOOL : "AI工具"
    + STORAGE : "存储管理"
    + TENANT : "租户管理"
    + SYSTEM : "系统管理"
    --
    + getDisplayName() : String <<获取显示名称>>
    + getAvailableActions() : List<Action> <<获取可用操作>>
    + getDefaultPermissions() : List<Permission> <<获取默认权限>>
    + isSystemResource() : Boolean <<是否系统资源>>
  }
  
  class "Action\n操作类型" as Action VALUE_OBJECT_COLOR {
    + CREATE : "创建"
    + READ : "读取"
    + UPDATE : "更新"
    + DELETE : "删除"
    + EXECUTE : "执行"
    + APPROVE : "审批"
    + AUDIT : "审计"
    + EXPORT : "导出"
    + IMPORT : "导入"
    --
    + getDisplayName() : String <<获取显示名称>>
    + requiresElevatedPrivileges() : Boolean <<是否需要提升权限>>
    + isDestructive() : Boolean <<是否破坏性操作>>
    + getAuditLevel() : AuditLevel <<获取审计级别>>
  }
  
  class "PermissionScope\n权限范围" as PermissionScope VALUE_OBJECT_COLOR {
    + GLOBAL : "全局"
    + TENANT : "租户"
    + USER : "用户"
    + RESOURCE_SPECIFIC : "资源特定"
    + DEPARTMENT : "部门"
    + PROJECT : "项目"
    --
    + getDisplayName() : String <<获取显示名称>>
    + isMoreRestrictiveThan(other: PermissionScope) : Boolean <<是否更严格>>
    + canAccessResource(resourceScope: PermissionScope) : Boolean <<是否可访问资源>>
    + getHierarchyLevel() : Integer <<获取层级>>
  }
  
  class "RoleType\n角色类型" as RoleType VALUE_OBJECT_COLOR {
    + SYSTEM_ADMIN : "系统管理员"
    + TENANT_ADMIN : "租户管理员"
    + BUSINESS_USER : "业务用户"
    + READONLY_USER : "只读用户"
    + GUEST : "访客"
    + API_USER : "API用户"
    --
    + getDisplayName() : String <<获取显示名称>>
    + getDefaultPermissions() : List<Permission> <<获取默认权限>>
    + getMaxPermissionScope() : PermissionScope <<获取最大权限范围>>
    + canManageUsers() : Boolean <<是否可管理用户>>
    + canAssignRoles() : Boolean <<是否可分配角色>>
  }
  
  class "AssignmentStatus\n分配状态" as AssignmentStatus VALUE_OBJECT_COLOR {
    + ACTIVE : "激活"
    + SUSPENDED : "暂停"
    + EXPIRED : "过期"
    + REVOKED : "撤销"
    + PENDING : "待生效"
    --
    + getDisplayName() : String <<获取显示名称>>
    + isEffective() : Boolean <<是否生效>>
    + canTransitionTo(newStatus: AssignmentStatus) : Boolean <<状态转换检查>>
    + requiresApproval() : Boolean <<是否需要审批>>
  }
}

' 关系定义 (Relationships)
Role ||--o{ Permission : "has permissions\n拥有权限"
Role ||--o{ UserRoleAssignment : "assigned to users\n分配给用户"
Role ||--|| RoleType : "has type\n拥有类型"
Permission ||--|| Resource : "applies to resource\n适用于资源"
Permission ||--|| Action : "defines action\n定义操作"
Permission ||--|| PermissionScope : "has scope\n拥有范围"
Permission ||--o{ PermissionCondition : "has conditions\n拥有条件"
UserRoleAssignment ||--|| AssignmentStatus : "has status\n拥有状态"
Role ||--o{ Role : "inherits from\n继承自"

@enduml
```

#### 5.3.3 设备管理聚合 (Device Management Aggregate)

**聚合根: Device（设备）**

- **业务含义 (Business Meaning)**: DTF平台管理的物理设备实体
- **核心职责 (Core Responsibilities)**:
  - 设备生命周期管理 (Device Lifecycle Management)
  - 设备状态监控 (Device Status Monitoring)
  - 设备配置管理 (Device Configuration Management)
  - 设备权限控制 (Device Access Control)
- **关键属性解析 (Key Attributes Analysis)**:
  - `serialNumber`: 设备序列号 (Device Serial Number)，全局唯一标识
  - `deviceModel`: 设备型号 (Device Model)，包含制造商和规格信息
  - `ownerId`: 设备所有者ID (Owner ID)，关联用户或租户
  - `status`: 设备状态 (Device Status)，实时运行状态
  - `capabilities`: 设备能力 (Device Capabilities)，支持的功能列表
  - `healthScore`: 健康度评分 (Health Score)，设备运行质量评估

**实体: DeviceConfiguration（设备配置）**

- **业务含义 (Business Meaning)**: 设备的个性化配置信息
- **核心职责 (Core Responsibilities)**:
  - 设备参数配置 (Device Parameter Configuration)
  - 功能模块开关 (Feature Module Toggle)
  - 性能参数调优 (Performance Parameter Tuning)
  - 配置版本管理 (Configuration Version Management)
- **关键属性解析 (Key Attributes Analysis)**:
  - `configType`: 配置类型 (Configuration Type)，如网络、性能、功能等
  - `parameters`: 配置参数 (Configuration Parameters)，JSON格式存储
  - `version`: 配置版本 (Configuration Version)，支持版本回滚
  - `isActive`: 是否激活 (Is Active)，配置生效状态

**实体: DeviceMetrics（设备指标）**

- **业务含义 (Business Meaning)**: 设备运行时的性能和状态指标
- **核心职责 (Core Responsibilities)**:
  - 性能指标收集 (Performance Metrics Collection)
  - 运行状态监控 (Runtime Status Monitoring)
  - 异常指标预警 (Abnormal Metrics Alert)
  - 历史数据分析 (Historical Data Analysis)
- **关键属性解析 (Key Attributes Analysis)**:
  - `metricType`: 指标类型 (Metric Type)，如CPU、内存、温度等
  - `value`: 指标数值 (Metric Value)，实时采集的数据
  - `threshold`: 阈值设置 (Threshold Setting)，预警触发条件
  - `timestamp`: 时间戳 (Timestamp)，数据采集时间

**值对象分析 (Value Objects Analysis)**:

- `DeviceModel`: 设备型号值对象 (Device Model Value Object)，包含制造商、型号、规格等信息
- `Status`: 设备状态枚举 (Device Status Enum)，标准化设备状态类型
- `HealthScore`: 健康度评分值对象 (Health Score Value Object)，包含评分算法和影响因子
- `Location`: 位置信息值对象 (Location Value Object)，支持地理位置和逻辑位置

```plantuml
@startuml DTF_Device_Domain_Model

title DTF设备管理领域模型 (DTF Device Management Domain Model)

!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define AGGREGATE_ROOT_COLOR #FFE6CC

package "设备管理聚合 (Device Management Aggregate)" {
  class "Device\n设备" as Device AGGREGATE_ROOT_COLOR {
    + deviceId : DeviceId
    + serialNumber : String <<设备序列号>>
    + deviceModel : DeviceModel <<设备型号>>
    + ownerId : UserId <<所有者ID>>
    + tenantId : TenantId <<租户ID>>
    + status : DeviceStatus <<设备状态>>
    + capabilities : List<Capability> <<设备能力>>
    + healthScore : HealthScore <<健康度评分>>
    + location : Location <<位置信息>>
    + registeredAt : DateTime <<注册时间>>
    + lastActiveAt : DateTime <<最后活跃时间>>
    --
    + updateStatus(status: DeviceStatus) <<更新状态>>
    + updateConfiguration(config: DeviceConfiguration) <<更新配置>>
    + recordMetrics(metrics: DeviceMetrics) <<记录指标>>
    + calculateHealthScore() : HealthScore <<计算健康度>>
    + checkPermission(userId: UserId) : Boolean <<权限检查>>
  }
  
  class "DeviceConfiguration\n设备配置" as DeviceConfiguration ENTITY_COLOR {
    + configId : ConfigId
    + deviceId : DeviceId <<设备ID>>
    + configType : ConfigType <<配置类型>>
    + parameters : JSON <<配置参数>>
    + version : String <<配置版本>>
    + isActive : Boolean <<是否激活>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + activate() <<激活配置>>
    + deactivate() <<停用配置>>
    + updateParameters(params: JSON) <<更新参数>>
    + createNewVersion() : DeviceConfiguration <<创建新版本>>
  }
  
  class "DeviceMetrics\n设备指标" as DeviceMetrics ENTITY_COLOR {
    + metricId : MetricId
    + deviceId : DeviceId <<设备ID>>
    + metricType : MetricType <<指标类型>>
    + value : Double <<指标数值>>
    + unit : String <<数值单位>>
    + threshold : Threshold <<阈值设置>>
    + timestamp : DateTime <<时间戳>>
    + isAbnormal : Boolean <<是否异常>>
    --
    + checkThreshold() : Boolean <<检查阈值>>
    + markAsAbnormal() <<标记异常>>
    + calculateTrend() : Trend <<计算趋势>>
  }
  
  class "DeviceModel\n设备型号" as DeviceModel VALUE_OBJECT_COLOR {
    + manufacturer : String <<制造商>>
    + modelName : String <<型号名称>>
    + version : String <<版本号>>
    + specifications : JSON <<规格参数>>
    + supportedFeatures : List<Feature> <<支持功能>>
    --
    + isCompatible(feature: Feature) : Boolean <<功能兼容性检查>>
    + getSpecification(key: String) : String <<获取规格参数>>
  }
  
  class "DeviceStatus\n设备状态" as DeviceStatus VALUE_OBJECT_COLOR {
    + OFFLINE : "离线"
    + ONLINE : "在线"
    + BUSY : "忙碌"
    + MAINTENANCE : "维护中"
    + ERROR : "故障"
    --
    + canTransitionTo(newStatus: DeviceStatus) : Boolean <<状态转换检查>>
    + getDisplayName() : String <<获取显示名称>>
  }
  
  class "HealthScore\n健康度评分" as HealthScore VALUE_OBJECT_COLOR {
    + score : Double <<评分(0-1)>>
    + factors : List<HealthFactor> <<影响因子>>
    + calculatedAt : DateTime <<计算时间>>
    + algorithm : String <<计算算法>>
    --
    + isHealthy() : Boolean <<是否健康>>
    + getLevel() : HealthLevel <<获取健康等级>>
    + getRecommendations() : List<String> <<获取建议>>
  }
  
  class "Location\n位置信息" as Location VALUE_OBJECT_COLOR {
    + latitude : Double <<纬度>>
    + longitude : Double <<经度>>
    + address : String <<地址>>
    + region : String <<区域>>
    + timezone : String <<时区>>
    --
    + distanceTo(other: Location) : Double <<计算距离>>
    + isInRegion(region: String) : Boolean <<区域检查>>
  }
}

' 关系定义 (Relationships)
Device ||--o{ DeviceConfiguration : "has configurations\n拥有配置"
Device ||--o{ DeviceMetrics : "generates metrics\n生成指标"
Device ||--|| DeviceModel : "has model\n拥有型号"
Device ||--|| DeviceStatus : "has status\n拥有状态"
Device ||--|| HealthScore : "has health score\n拥有健康度"
Device ||--|| Location : "has location\n拥有位置"

@enduml
```

#### 5.3.4 内容管理聚合 (Content Management Aggregate)

**聚合根: DesignWork（设计作品）**

- **业务含义 (Business Meaning)**: 用户创作的设计作品和项目
- **核心职责 (Core Responsibilities)**:
  - 设计作品生命周期管理 (Design Work Lifecycle Management)
  - 版本控制和历史管理 (Version Control and History Management)
  - 协作和分享管理 (Collaboration and Sharing Management)
  - 作品权限和隐私控制 (Work Permission and Privacy Control)
- **关键属性解析 (Key Attributes Analysis)**:
  - `workId`: 作品唯一标识 (Work Unique Identifier)
  - `title`: 作品标题 (Work Title)，用户自定义名称
  - `creatorId`: 创作者ID (Creator ID)，关联用户账户
  - `category`: 作品分类 (Work Category)，便于组织和搜索
  - `status`: 作品状态 (Work Status)，如草稿、已发布、已删除
  - `visibility`: 可见性设置 (Visibility Setting)，隐私控制

**实体: Template（模板）**

- **业务含义 (Business Meaning)**: 可复用的设计模板
- **核心职责 (Core Responsibilities)**:
  - 模板创建和编辑 (Template Creation and Editing)
  - 模板分类和标签管理 (Template Category and Tag Management)
  - 模板使用统计 (Template Usage Statistics)
  - 模板质量评估 (Template Quality Assessment)
- **关键属性解析 (Key Attributes Analysis)**:
  - `templateId`: 模板唯一标识 (Template Unique Identifier)
  - `name`: 模板名称 (Template Name)
  - `description`: 模板描述 (Template Description)
  - `category`: 模板分类 (Template Category)
  - `tags`: 标签列表 (Tag List)，便于搜索和分类
  - `usageCount`: 使用次数 (Usage Count)，热度统计

**实体: GalleryItem（图库项目）**

- **业务含义 (Business Meaning)**: 图库中的可购买或免费资源
- **核心职责 (Core Responsibilities)**:
  - 图库内容管理 (Gallery Content Management)
  - 版权和授权管理 (Copyright and License Management)
  - 定价和销售管理 (Pricing and Sales Management)
  - 内容审核和质量控制 (Content Review and Quality Control)
- **关键属性解析 (Key Attributes Analysis)**:
  - `itemId`: 项目唯一标识 (Item Unique Identifier)
  - `title`: 项目标题 (Item Title)
  - `uploaderId`: 上传者ID (Uploader ID)
  - `pricingInfo`: 定价信息 (Pricing Information)
  - `copyrightInfo`: 版权信息 (Copyright Information)
  - `downloadCount`: 下载次数 (Download Count)

**值对象分析 (Value Objects Analysis)**:

- `Category`: 分类值对象 (Category Value Object)，支持层级分类和多维度标签
- `PricingInfo`: 定价信息值对象 (Pricing Info Value Object)，支持多种定价模式和货币
- `CopyrightInfo`: 版权信息值对象 (Copyright Info Value Object)，包含版权声明和使用许可
- `Visibility`: 可见性枚举 (Visibility Enum)，控制内容访问权限

```plantuml
@startuml DTF_Content_Domain_Model

title DTF内容管理领域模型 (DTF Content Management Domain Model)

!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define AGGREGATE_ROOT_COLOR #FFE6CC

package "内容管理聚合 (Content Management Aggregate)" {
  class "DesignWork\n设计作品" as DesignWork AGGREGATE_ROOT_COLOR {
    + workId : WorkId
    + title : String <<作品标题>>
    + description : String <<作品描述>>
    + creatorId : UserId <<创作者ID>>
    + tenantId : TenantId <<租户ID>>
    + category : Category <<作品分类>>
    + status : WorkStatus <<作品状态>>
    + visibility : Visibility <<可见性设置>>
    + fileReferences : List<FileReference> <<文件引用>>
    + tags : List<Tag> <<标签列表>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    + publishedAt : DateTime <<发布时间>>
    --
    + publish() <<发布作品>>
    + unpublish() <<取消发布>>
    + updateVisibility(visibility: Visibility) <<更新可见性>>
    + addTag(tag: Tag) <<添加标签>>
    + removeTag(tag: Tag) <<移除标签>>
    + createTemplate() : Template <<创建模板>>
    + checkPermission(userId: UserId) : Boolean <<权限检查>>
  }
  
  class "Template\n模板" as Template ENTITY_COLOR {
    + templateId : TemplateId
    + name : String <<模板名称>>
    + description : String <<模板描述>>
    + creatorId : UserId <<创作者ID>>
    + category : Category <<模板分类>>
    + tags : List<Tag> <<标签列表>>
    + templateData : JSON <<模板数据>>
    + previewImage : String <<预览图片>>
    + usageCount : Integer <<使用次数>>
    + rating : Double <<评分>>
    + isPublic : Boolean <<是否公开>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + use() <<使用模板>>
    + rate(score: Double) <<评分>>
    + updateData(data: JSON) <<更新数据>>
    + makePublic() <<设为公开>>
    + makePrivate() <<设为私有>>
  }
  
  class "GalleryItem\n图库项目" as GalleryItem ENTITY_COLOR {
    + itemId : ItemId
    + title : String <<项目标题>>
    + description : String <<项目描述>>
    + uploaderId : UserId <<上传者ID>>
    + category : Category <<项目分类>>
    + tags : List<Tag> <<标签列表>>
    + pricingInfo : PricingInfo <<定价信息>>
    + copyrightInfo : CopyrightInfo <<版权信息>>
    + fileUrl : String <<文件URL>>
    + previewUrl : String <<预览URL>>
    + downloadCount : Integer <<下载次数>>
    + rating : Double <<评分>>
    + status : ItemStatus <<项目状态>>
    + createdAt : DateTime <<创建时间>>
    + approvedAt : DateTime <<审核通过时间>>
    --
    + download() <<下载>>
    + purchase() <<购买>>
    + rate(score: Double) <<评分>>
    + approve() <<审核通过>>
    + reject(reason: String) <<审核拒绝>>
    + updatePricing(pricing: PricingInfo) <<更新定价>>
  }
  
  class "Category\n分类" as Category VALUE_OBJECT_COLOR {
    + id : String <<分类ID>>
    + name : String <<分类名称>>
    + parentId : String <<父分类ID>>
    + level : Integer <<分类层级>>
    + path : String <<分类路径>>
    + description : String <<分类描述>>
    --
    + isSubCategoryOf(parent: Category) : Boolean <<是否为子分类>>
    + getFullPath() : String <<获取完整路径>>
    + getChildren() : List<Category> <<获取子分类>>
  }
  
  class "PricingInfo\n定价信息" as PricingInfo VALUE_OBJECT_COLOR {
    + priceType : PriceType <<定价类型>>
    + amount : Money <<价格金额>>
    + currency : Currency <<货币类型>>
    + discountInfo : DiscountInfo <<折扣信息>>
    + validFrom : DateTime <<有效开始时间>>
    + validTo : DateTime <<有效结束时间>>
    --
    + isFree() : Boolean <<是否免费>>
    + calculateFinalPrice() : Money <<计算最终价格>>
    + isValidAt(time: DateTime) : Boolean <<检查时间有效性>>
  }
  
  class "CopyrightInfo\n版权信息" as CopyrightInfo VALUE_OBJECT_COLOR {
    + owner : String <<版权所有者>>
    + licenseType : LicenseType <<许可证类型>>
    + usageRights : List<UsageRight> <<使用权限>>
    + restrictions : List<Restriction> <<使用限制>>
    + copyrightYear : Integer <<版权年份>>
    + attribution : String <<署名要求>>
    --
    + canUseFor(purpose: UsagePurpose) : Boolean <<使用权限检查>>
    + getAttributionText() : String <<获取署名文本>>
    + isCommercialAllowed() : Boolean <<是否允许商用>>
  }
  
  class "Visibility\n可见性" as Visibility VALUE_OBJECT_COLOR {
    + PUBLIC : "公开"
    + PRIVATE : "私有"
    + TENANT_ONLY : "仅租户"
    + SHARED : "已分享"
    --
    + canAccessBy(userId: UserId, tenantId: TenantId) : Boolean <<访问权限检查>>
    + getDisplayName() : String <<获取显示名称>>
  }
}

' 关系定义 (Relationships)
DesignWork ||--o{ Template : "can create templates\n可创建模板"
DesignWork ||--|| Category : "belongs to category\n属于分类"
DesignWork ||--|| Visibility : "has visibility\n拥有可见性"
Template ||--|| Category : "belongs to category\n属于分类"
GalleryItem ||--|| Category : "belongs to category\n属于分类"
GalleryItem ||--|| PricingInfo : "has pricing\n拥有定价"
GalleryItem ||--|| CopyrightInfo : "has copyright\n拥有版权"

@enduml
```

#### 5.3.5 商务交易聚合 (Commerce Transaction Aggregate)

**聚合根: Order（订单）**

- **业务含义 (Business Meaning)**: 用户的购买订单和交易记录
- **核心职责 (Core Responsibilities)**:
  - 订单生命周期管理 (Order Lifecycle Management)
  - 支付流程协调 (Payment Process Coordination)
  - 库存和商品管理 (Inventory and Product Management)
  - 订单状态跟踪 (Order Status Tracking)
- **关键属性解析 (Key Attributes Analysis)**:
  - `orderId`: 订单唯一标识 (Order Unique Identifier)
  - `customerId`: 客户ID (Customer ID)，关联用户账户
  - `orderItems`: 订单项目列表 (Order Items List)，购买的商品清单
  - `totalAmount`: 订单总金额 (Total Amount)，包含税费和折扣
  - `status`: 订单状态 (Order Status)，如待支付、已支付、已完成
  - `paymentInfo`: 支付信息 (Payment Information)，支付方式和状态

**实体: OrderItem（订单项目）**

- **业务含义 (Business Meaning)**: 订单中的具体商品项目
- **核心职责 (Core Responsibilities)**:
  - 商品信息管理 (Product Information Management)
  - 数量和价格计算 (Quantity and Price Calculation)
  - 商品状态跟踪 (Product Status Tracking)
  - 退换货处理 (Return and Exchange Processing)
- **关键属性解析 (Key Attributes Analysis)**:
  - `itemId`: 项目唯一标识 (Item Unique Identifier)
  - `productId`: 商品ID (Product ID)，关联商品信息
  - `quantity`: 购买数量 (Purchase Quantity)
  - `unitPrice`: 单价 (Unit Price)，商品单价
  - `totalPrice`: 总价 (Total Price)，数量×单价
  - `status`: 项目状态 (Item Status)，如正常、退货、换货

**实体: Payment（支付）**

- **业务含义 (Business Meaning)**: 订单的支付记录和交易信息
- **核心职责 (Core Responsibilities)**:
  - 支付流程管理 (Payment Process Management)
  - 多支付方式支持 (Multiple Payment Methods Support)
  - 支付安全验证 (Payment Security Verification)
  - 退款和对账处理 (Refund and Reconciliation Processing)
- **关键属性解析 (Key Attributes Analysis)**:
  - `paymentId`: 支付唯一标识 (Payment Unique Identifier)
  - `orderId`: 关联订单ID (Associated Order ID)
  - `amount`: 支付金额 (Payment Amount)
  - `paymentMethod`: 支付方式 (Payment Method)，如信用卡、PayPal
  - `status`: 支付状态 (Payment Status)，如处理中、成功、失败
  - `transactionId`: 第三方交易ID (Third-party Transaction ID)

**实体: PaymentMethod（支付方式）**

- **业务含义 (Business Meaning)**: 用户绑定的支付方式
- **核心职责 (Core Responsibilities)**:
  - 支付方式管理 (Payment Method Management)
  - 支付安全验证 (Payment Security Verification)
  - 支付方式选择 (Payment Method Selection)
  - 支付失败处理 (Payment Failure Handling)
- **关键属性解析 (Key Attributes Analysis)**:
  - `methodId`: 支付方式ID (Payment Method ID)
  - `userId`: 用户ID (User ID)，关联用户账户
  - `type`: 支付类型 (Payment Type)，如信用卡、PayPal、Apple Pay、Google Pay等
  - `provider`: 支付提供商 (Payment Provider)，如Stripe、PayPal、银行等
  - `accountInfo`: 账户信息 (Account Information)，加密存储的支付账户信息
  - `isDefault`: 是否默认 (Is Default)，支持用户设置默认支付方式

**值对象分析 (Value Objects Analysis)**:

- `ProductCategory`: 商品分类枚举 (Product Category Enum)，标准化商品类型
- `OrderStatus`: 订单状态枚举 (Order Status Enum)，定义订单生命周期
- `PaymentStatus`: 支付状态枚举 (Payment Status Enum)，支付流程状态
- `Money`: 金额值对象 (Money Value Object)，包含金额和货币类型

```plantuml
@startuml DTF_Commerce_Domain_Model

title DTF商务交易领域模型 (DTF Commerce Transaction Domain Model)

!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define AGGREGATE_ROOT_COLOR #FFE6CC

package "商务交易聚合 (Commerce Transaction Aggregate)" {
  class "Order\n订单" as Order AGGREGATE_ROOT_COLOR {
    + orderId : OrderId
    + orderNumber : String <<订单编号>>
    + customerId : UserId <<客户ID>>
    + tenantId : TenantId <<租户ID>>
    + orderItems : List<OrderItem> <<订单项目>>
    + totalAmount : Money <<订单总金额>>
    + discountAmount : Money <<折扣金额>>
    + taxAmount : Money <<税费金额>>
    + status : OrderStatus <<订单状态>>
    + paymentInfo : PaymentInfo <<支付信息>>
    + shippingAddress : Address <<配送地址>>
    + billingAddress : Address <<账单地址>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    + completedAt : DateTime <<完成时间>>
    --
    + addItem(item: OrderItem) <<添加项目>>
    + removeItem(itemId: ItemId) <<移除项目>>
    + calculateTotal() : Money <<计算总金额>>
    + confirm() <<确认订单>>
    + cancel() <<取消订单>>
    + complete() <<完成订单>>
    + refund(amount: Money) <<退款>>
  }
  
  class "OrderItem\n订单项目" as OrderItem ENTITY_COLOR {
    + itemId : ItemId
    + orderId : OrderId <<订单ID>>
    + productId : ProductId <<商品ID>>
    + productName : String <<商品名称>>
    + productCategory : ProductCategory <<商品分类>>
    + quantity : Integer <<购买数量>>
    + unitPrice : Money <<单价>>
    + totalPrice : Money <<总价>>
    + discountAmount : Money <<折扣金额>>
    + status : ItemStatus <<项目状态>>
    + metadata : JSON <<商品元数据>>
    --
    + updateQuantity(quantity: Integer) <<更新数量>>
    + applyDiscount(discount: Money) <<应用折扣>>
    + calculateTotal() : Money <<计算总价>>
    + canReturn() : Boolean <<是否可退货>>
    + return(reason: String) <<退货>>
  }
  
  class "Payment\n支付" as Payment ENTITY_COLOR {
    + paymentId : PaymentId
    + orderId : OrderId <<订单ID>>
    + amount : Money <<支付金额>>
    + paymentMethod : PaymentMethod <<支付方式>>
    + status : PaymentStatus <<支付状态>>
    + transactionId : String <<第三方交易ID>>
    + gatewayResponse : JSON <<网关响应>>
    + failureReason : String <<失败原因>>
    + processedAt : DateTime <<处理时间>>
    + completedAt : DateTime <<完成时间>>
    --
    + process() <<处理支付>>
    + confirm() <<确认支付>>
    + fail(reason: String) <<支付失败>>
    + refund(amount: Money) <<退款>>
    + retry() <<重试支付>>
  }
  
  class "PaymentMethod\n支付方式" as PaymentMethod ENTITY_COLOR {
    + methodId : MethodId
    + userId : UserId <<用户ID>>
    + type : PaymentType <<支付类型>>
    + provider : String <<支付提供商>>
    + accountInfo : EncryptedData <<账户信息>>
    + displayName : String <<显示名称>>
    + isDefault : Boolean <<是否默认>>
    + isActive : Boolean <<是否激活>>
    + expiresAt : DateTime <<过期时间>>
    + createdAt : DateTime <<创建时间>>
    + lastUsedAt : DateTime <<最后使用时间>>
    --
    + setAsDefault() <<设为默认>>
    + activate() <<激活>>
    + deactivate() <<停用>>
    + updateInfo(info: EncryptedData) <<更新信息>>
    + isExpired() : Boolean <<是否过期>>
  }
  
  class "ProductCategory\n商品分类" as ProductCategory VALUE_OBJECT_COLOR {
    + POINTS_PACKAGE : "积分包"
    + DESIGN_TEMPLATE : "设计模板"
    + GALLERY_ITEM : "图库项目"
    + STORAGE_UPGRADE : "存储升级"
    + PREMIUM_FEATURE : "高级功能"
    --
    + getDisplayName() : String <<获取显示名称>>
    + isDigitalProduct() : Boolean <<是否数字商品>>
    + requiresShipping() : Boolean <<是否需要配送>>
  }
  
  class "OrderStatus\n订单状态" as OrderStatus VALUE_OBJECT_COLOR {
    + PENDING_PAYMENT : "待支付"
    + PAID : "已支付"
    + PROCESSING : "处理中"
    + SHIPPED : "已发货"
    + DELIVERED : "已送达"
    + COMPLETED : "已完成"
    + CANCELLED : "已取消"
    + REFUNDED : "已退款"
    --
    + canTransitionTo(newStatus: OrderStatus) : Boolean <<状态转换检查>>
    + getDisplayName() : String <<获取显示名称>>
    + isTerminalStatus() : Boolean <<是否终态>>
  }
  
  class "PaymentStatus\n支付状态" as PaymentStatus VALUE_OBJECT_COLOR {
    + PENDING : "待处理"
    + PROCESSING : "处理中"
    + SUCCESS : "成功"
    + FAILED : "失败"
    + CANCELLED : "已取消"
    + REFUNDED : "已退款"
    --
    + isSuccessful() : Boolean <<是否成功>>
    + isFinal() : Boolean <<是否最终状态>>
    + getDisplayName() : String <<获取显示名称>>
  }
  
  class "Money\n金额" as Money VALUE_OBJECT_COLOR {
    + amount : BigDecimal <<金额>>
    + currency : Currency <<货币类型>>
    --
    + add(other: Money) : Money <<加法>>
    + subtract(other: Money) : Money <<减法>>
    + multiply(factor: BigDecimal) : Money <<乘法>>
    + divide(divisor: BigDecimal) : Money <<除法>>
    + isZero() : Boolean <<是否为零>>
    + isPositive() : Boolean <<是否为正>>
    + convertTo(currency: Currency) : Money <<货币转换>>
  }
}

' 关系定义 (Relationships)
Order ||--o{ OrderItem : "contains items\n包含项目"
Order ||--o{ Payment : "has payments\n拥有支付"
Order ||--|| OrderStatus : "has status\n拥有状态"
OrderItem ||--|| ProductCategory : "belongs to category\n属于分类"
Payment ||--|| PaymentMethod : "uses method\n使用方式"
Payment ||--|| PaymentStatus : "has status\n拥有状态"
Order ||--|| Money : "has total amount\n拥有总金额"
OrderItem ||--|| Money : "has price\n拥有价格"
Payment ||--|| Money : "has amount\n拥有金额"

@enduml
```

#### 5.3.6 合规审计聚合 (Compliance Audit Aggregate)

**聚合根: DataSubject（数据主体）**

- **业务含义 (Business Meaning)**: 享有数据权利的个人或实体
- **核心职责 (Core Responsibilities)**:
  - 数据主体权利管理 (Data Subject Rights Management)
  - 个人数据处理同意管理 (Personal Data Processing Consent Management)
  - 数据主体请求处理 (Data Subject Request Processing)
  - 隐私偏好设置管理 (Privacy Preference Settings Management)
- **关键属性解析 (Key Attributes Analysis)**:
  - `subjectId`: 数据主体唯一标识 (Data Subject Unique Identifier)
  - `userId`: 关联用户ID (Associated User ID)
  - `dataProtectionRegion`: 数据保护区域 (Data Protection Region)，如EU、CA、US
  - `consentRecords`: 同意记录列表 (Consent Records List)
  - `dataProcessingHistory`: 数据处理历史 (Data Processing History)
  - `privacyPreferences`: 隐私偏好设置 (Privacy Preferences)

**实体: ConsentRecord（同意记录）**

- **业务含义 (Business Meaning)**: 用户对数据处理的同意记录
- **核心职责 (Core Responsibilities)**:
  - 同意状态管理 (Consent Status Management)
  - 同意范围定义 (Consent Scope Definition)
  - 同意撤回处理 (Consent Withdrawal Processing)
  - 同意历史追踪 (Consent History Tracking)
- **关键属性解析 (Key Attributes Analysis)**:
  - `consentId`: 同意记录唯一标识 (Consent Record Unique Identifier)
  - `subjectId`: 数据主体ID (Data Subject ID)
  - `processingPurpose`: 处理目的 (Processing Purpose)，如营销、分析、服务提供
  - `dataCategories`: 数据类别 (Data Categories)，涉及的个人数据类型
  - `consentStatus`: 同意状态 (Consent Status)，如已同意、已撤回、已过期
  - `legalBasis`: 法律依据 (Legal Basis)，数据处理的合法性基础

**实体: ProcessingActivity（处理活动）**

- **业务含义 (Business Meaning)**: 对个人数据的具体处理活动
- **核心职责 (Core Responsibilities)**:
  - 处理活动记录 (Processing Activity Recording)
  - 处理目的管理 (Processing Purpose Management)
  - 数据流向跟踪 (Data Flow Tracking)
  - 处理合规性检查 (Processing Compliance Check)
- **关键属性解析 (Key Attributes Analysis)**:
  - `activityId`: 活动唯一标识 (Activity Unique Identifier)
  - `activityName`: 活动名称 (Activity Name)
  - `purpose`: 处理目的 (Processing Purpose)
  - `dataCategories`: 涉及数据类别 (Involved Data Categories)
  - `recipients`: 数据接收方 (Data Recipients)
  - `retentionPeriod`: 保留期限 (Retention Period)

**实体: AuditLog（审计日志）**

- **业务含义 (Business Meaning)**: 系统操作和数据访问的审计记录
- **核心职责 (Core Responsibilities)**:
  - 操作行为记录 (Operation Behavior Recording)
  - 数据访问追踪 (Data Access Tracking)
  - 安全事件监控 (Security Event Monitoring)
  - 合规证据收集 (Compliance Evidence Collection)
- **关键属性解析 (Key Attributes Analysis)**:
  - `logId`: 日志唯一标识 (Log Unique Identifier)
  - `userId`: 操作用户ID (Operating User ID)
  - `action`: 操作行为 (Operation Action)，如创建、读取、更新、删除
  - `resource`: 操作资源 (Operation Resource)，被操作的数据或功能
  - `timestamp`: 操作时间戳 (Operation Timestamp)
  - `ipAddress`: IP地址 (IP Address)，操作来源IP
  - `userAgent`: 用户代理 (User Agent)，客户端信息

**实体: DataRetention（数据保留）**

- **业务含义 (Business Meaning)**: 数据保留和删除的管理
- **核心职责 (Core Responsibilities)**:
  - 数据保留期限管理 (Data Retention Period Management)
  - 数据删除调度 (Data Deletion Scheduling)
  - 数据保留政策执行 (Data Retention Policy Execution)
  - 数据删除审计 (Data Deletion Audit)
- **关键属性解析 (Key Attributes Analysis)**:
  - `retentionId`: 保留记录唯一标识 (Retention Record Unique Identifier)
  - `dataType`: 数据类型 (Data Type)，不同类型数据可能有不同的保留要求
  - `retentionPeriod`: 保留期限 (Retention Period)，数据需要保留的时间长度
  - `deletionDate`: 删除日期 (Deletion Date)，计划删除数据的具体日期
  - `policy`: 保留政策 (Retention Policy)，数据保留的具体政策和规则

**值对象分析 (Value Objects Analysis)**:

- `DataProtectionLaw`: 数据保护法律枚举 (Data Protection Law Enum)，如GDPR、CCPA等
- `ProcessingPurpose`: 处理目的枚举 (Processing Purpose Enum)，标准化数据处理目的
- `LegalBasis`: 法律依据枚举 (Legal Basis Enum)，数据处理的合法性基础
- `DataSubjectRight`: 数据主体权利枚举 (Data Subject Right Enum)，用户享有的数据权利
- `ConsentStatus`: 同意状态枚举 (Consent Status Enum)，同意记录的状态
- `DataCategory`: 数据类别枚举 (Data Category Enum)，个人数据的分类

```plantuml
@startuml DTF_Compliance_Domain_Model

title DTF合规审计领域模型 (DTF Compliance Audit Domain Model)

!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define AGGREGATE_ROOT_COLOR #FFE6CC

package "合规审计聚合 (Compliance Audit Aggregate)" {
  class "DataSubject\n数据主体" as DataSubject AGGREGATE_ROOT_COLOR {
    + subjectId : SubjectId
    + userId : UserId <<关联用户ID>>
    + email : String <<邮箱地址>>
    + dataProtectionRegion : Region <<数据保护区域>>
    + consentRecords : List<ConsentRecord> <<同意记录>>
    + privacyPreferences : PrivacyPreferences <<隐私偏好>>
    + registeredAt : DateTime <<注册时间>>
    + lastConsentUpdate : DateTime <<最后同意更新时间>>
    --
    + grantConsent(purpose: ProcessingPurpose) <<授予同意>>
    + withdrawConsent(consentId: ConsentId) <<撤回同意>>
    + updatePrivacyPreferences(preferences: PrivacyPreferences) <<更新隐私偏好>>
    + requestDataExport() : DataExportRequest <<请求数据导出>>
    + requestDataDeletion() : DataDeletionRequest <<请求数据删除>>
    + hasValidConsent(purpose: ProcessingPurpose) : Boolean <<检查有效同意>>
  }
  
  class "ConsentRecord\n同意记录" as ConsentRecord ENTITY_COLOR {
    + consentId : ConsentId
    + subjectId : SubjectId <<数据主体ID>>
    + processingPurpose : ProcessingPurpose <<处理目的>>
    + dataCategories : List<DataCategory> <<数据类别>>
    + consentStatus : ConsentStatus <<同意状态>>
    + legalBasis : LegalBasis <<法律依据>>
    + consentText : String <<同意文本>>
    + consentMethod : ConsentMethod <<同意方式>>
    + grantedAt : DateTime <<授予时间>>
    + withdrawnAt : DateTime <<撤回时间>>
    + expiresAt : DateTime <<过期时间>>
    --
    + withdraw() <<撤回同意>>
    + renew() <<续期同意>>
    + isValid() : Boolean <<是否有效>>
    + isExpired() : Boolean <<是否过期>>
    + getConsentScope() : ConsentScope <<获取同意范围>>
  }
  
  class "ProcessingActivity\n处理活动" as ProcessingActivity ENTITY_COLOR {
    + activityId : ActivityId
    + activityName : String <<活动名称>>
    + description : String <<活动描述>>
    + purpose : ProcessingPurpose <<处理目的>>
    + dataCategories : List<DataCategory> <<数据类别>>
    + recipients : List<Recipient> <<数据接收方>>
    + retentionPeriod : Period <<保留期限>>
    + securityMeasures : List<SecurityMeasure> <<安全措施>>
    + dataTransfers : List<DataTransfer> <<数据传输>>
    + createdAt : DateTime <<创建时间>>
    + updatedAt : DateTime <<更新时间>>
    --
    + addDataCategory(category: DataCategory) <<添加数据类别>>
    + removeDataCategory(category: DataCategory) <<移除数据类别>>
    + updateRetentionPeriod(period: Period) <<更新保留期限>>
    + addRecipient(recipient: Recipient) <<添加接收方>>
    + isCompliant() : Boolean <<是否合规>>
  }
  
  class "AuditLog\n审计日志" as AuditLog ENTITY_COLOR {
    + logId : LogId
    + userId : UserId <<操作用户ID>>
    + subjectId : SubjectId <<数据主体ID>>
    + action : AuditAction <<操作行为>>
    + resource : String <<操作资源>>
    + resourceId : String <<资源ID>>
    + details : JSON <<操作详情>>
    + timestamp : DateTime <<操作时间戳>>
    + ipAddress : String <<IP地址>>
    + userAgent : String <<用户代理>>
    + sessionId : String <<会话ID>>
    + result : AuditResult <<操作结果>>
    --
    + isDataAccess() : Boolean <<是否数据访问>>
    + isSecurityEvent() : Boolean <<是否安全事件>>
    + getEventSeverity() : Severity <<获取事件严重性>>
    + generateReport() : AuditReport <<生成审计报告>>
  }
  
  class "DataRetention\n数据保留" as DataRetention ENTITY_COLOR {
    + retentionId : RetentionId
    + dataType : DataType <<数据类型>>
    + dataIdentifier : String <<数据标识>>
    + retentionPeriod : Period <<保留期限>>
    + createdAt : DateTime <<创建时间>>
    + deletionDate : DateTime <<删除日期>>
    + policy : RetentionPolicy <<保留政策>>
    + status : RetentionStatus <<保留状态>>
    + deletedAt : DateTime <<删除时间>>
    --
    + scheduleForDeletion() <<安排删除>>
    + postponeDeletion(reason: String) <<推迟删除>>
    + executeDelete() <<执行删除>>
    + isExpired() : Boolean <<是否过期>>
    + canBeDeleted() : Boolean <<是否可删除>>
  }
  
  class "ProcessingPurpose\n处理目的" as ProcessingPurpose VALUE_OBJECT_COLOR {
    + SERVICE_PROVISION : "服务提供"
    + MARKETING : "营销推广"
    + ANALYTICS : "数据分析"
    + SECURITY : "安全保护"
    + LEGAL_COMPLIANCE : "法律合规"
    + RESEARCH : "研究开发"
    --
    + getDisplayName() : String <<获取显示名称>>
    + getDescription() : String <<获取描述>>
    + isMarketingPurpose() : Boolean <<是否营销目的>>
  }
  
  class "ConsentStatus\n同意状态" as ConsentStatus VALUE_OBJECT_COLOR {
    + GRANTED : "已同意"
    + WITHDRAWN : "已撤回"
    + EXPIRED : "已过期"
    + PENDING : "待确认"
    --
    + isActive() : Boolean <<是否激活>>
    + canProcess() : Boolean <<是否可处理>>
    + getDisplayName() : String <<获取显示名称>>
  }
  
  class "DataCategory\n数据类别" as DataCategory VALUE_OBJECT_COLOR {
    + PERSONAL_IDENTITY : "个人身份"
    + CONTACT_INFO : "联系信息"
    + FINANCIAL_DATA : "财务数据"
    + BEHAVIORAL_DATA : "行为数据"
    + TECHNICAL_DATA : "技术数据"
    + SENSITIVE_DATA : "敏感数据"
    --
    + isSensitive() : Boolean <<是否敏感>>
    + getProtectionLevel() : ProtectionLevel <<获取保护级别>>
    + getDisplayName() : String <<获取显示名称>>
  }
  
  class "LegalBasis\n法律依据" as LegalBasis VALUE_OBJECT_COLOR {
    + CONSENT : "同意"
    + CONTRACT : "合同履行"
    + LEGAL_OBLIGATION : "法律义务"
    + VITAL_INTERESTS : "重要利益"
    + PUBLIC_TASK : "公共任务"
    + LEGITIMATE_INTERESTS : "合法利益"
    --
    + requiresConsent() : Boolean <<是否需要同意>>
    + getDisplayName() : String <<获取显示名称>>
    + isValidFor(purpose: ProcessingPurpose) : Boolean <<是否适用于目的>>
  }
}

' 关系定义 (Relationships)
DataSubject ||--o{ ConsentRecord : "has consent records\n拥有同意记录"
DataSubject ||--o{ ProcessingActivity : "involved in activities\n参与活动"
DataSubject ||--o{ AuditLog : "generates audit logs\n生成审计日志"
DataSubject ||--o{ DataRetention : "subject to retention\n受保留约束"
ConsentRecord ||--|| ProcessingPurpose : "for purpose\n用于目的"
ConsentRecord ||--|| ConsentStatus : "has status\n拥有状态"
ConsentRecord ||--|| LegalBasis : "based on\n基于"
ProcessingActivity ||--|| ProcessingPurpose : "for purpose\n用于目的"
ProcessingActivity ||--o{ DataCategory : "processes categories\n处理类别"
AuditLog ||--|| DataCategory : "involves category\n涉及类别"
DataRetention ||--|| DataCategory : "retains category\n保留类别"

@enduml
```

## 📊 6. 数据库ER图

### 6.1 核心数据库设计

基于现有的`ddl_mvp_opti.sql`表设计，结合DDD领域模型，设计完整的数据库ER图:

```plantuml
@startuml DTF_Database_ER_Diagram

title DTF玩图平台核心数据库ER图 - 基于实际SQL结构优化版

' 全局身份和租户层
package "GlobalIdentityLayer" {
  entity dtf_global_identity {
    * id : BIGINT <<PK>>
    * global_user_id : VARCHAR(64) <<UK>>
    --
    email_hash : VARCHAR(64) <<索引>>
    phone_hash : VARCHAR(64)
    identity_fingerprint : VARCHAR(128)
    identity_type : TINYINT <<0-官方,1-厂商,2-消费者,3-代理>>
    verification_level : TINYINT <<0-未验证,1-邮箱,2-手机,3-实名,4-企业>>
    risk_score : DECIMAL(3,2) <<风险评分0-1>>
    global_status : TINYINT <<0-待激活,1-活跃,2-暂停,3-已删除>>
    suspension_reason : VARCHAR(200)
    gdpr_consent_version : VARCHAR(20)
    data_retention_until : DATE
    last_privacy_policy_accepted_at : DATETIME
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT <<0-未删除,1-已删除>>
  }

  entity dtf_deployment_instance {
    * id : BIGINT <<PK>>
    * instance_code : VARCHAR(50) <<UK>>
    --
    instance_name : VARCHAR(100)
    deployment_type : TINYINT <<0-SaaS,1-私有云,2-混合云,3-本地部署>>
    region_code : VARCHAR(10) <<US-EAST-1/US-WEST-2等>>
    primary_domain : VARCHAR(100)
    api_gateway_url : VARCHAR(200)
    cdn_domain : VARCHAR(100)
    database_config : JSON <<数据库配置信息>>
    storage_config : JSON <<存储配置信息(AWS S3等)>>
    cache_config : JSON <<缓存配置信息(Redis等)>>
    data_residency_rules : JSON <<数据驻留规则>>
    compliance_certifications : JSON <<合规认证列表(SOX/PCI/HIPAA等)>>
    status : TINYINT <<0-规划,1-部署中,2-运行,3-维护,4-停用>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 租户管理层
package "TenantManagementLayer" {
  entity dtf_tenant {
    * id : BIGINT <<PK>>
    * tenant_code : VARCHAR(50) <<UK>>
    * deployment_instance_id : BIGINT <<FK>>
    parent_tenant_id : BIGINT <<FK,支持代理商层级>>
    --
    tenant_name : VARCHAR(100)
    tenant_name_en : VARCHAR(100)
    tenant_type : TINYINT <<0-官方,1-设备厂商,2-代理商,3-企业客户>>
    tenant_hierarchy : VARCHAR(200) <<层级路径,如/1/2/3>>
    hierarchy_level : INT <<层级深度>>
    isolation_level : TINYINT <<1-数据库,2-模式,3-表>>
    brand_config : JSON <<品牌配置(Logo/颜色/字体)>>
    theme_config : JSON <<主题配置>>
    logo_url : VARCHAR(500)
    custom_domain : VARCHAR(100)
    feature_config : JSON <<功能配置开关>>
    module_permissions : JSON <<模块权限配置>>
    api_rate_limits : JSON <<API限流配置>>
    user_quota : INT <<用户配额>>
    storage_quota_gb : INT <<存储配额(GB)>>
    api_quota_daily : INT <<每日API调用配额>>
    billing_model : TINYINT <<0-免费,1-订阅,2-按量付费,3-企业版>>
    billing_config : JSON <<计费规则配置>>
    subscription_plan : VARCHAR(50)
    data_processing_purposes : JSON <<数据处理目的>>
    retention_policies : JSON <<数据保留策略>>
    privacy_settings : JSON <<隐私设置>>
    tenant_status : TINYINT <<0-待激活,1-活跃,2-暂停,3-已删除>>
    suspension_reason : VARCHAR(200)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_tenant_user_account {
    * id : BIGINT <<PK>>
    * global_user_id : VARCHAR(64) <<FK>>
    * tenant_id : BIGINT <<FK>>
    * email : VARCHAR(100) <<UK,租户内唯一>>
    --
    username : VARCHAR(50) <<UK,租户内唯一>>
    password_hash : VARCHAR(255) <<BCrypt加密>>
    salt : VARCHAR(32)
    user_type : TINYINT <<0-消费者,1-厂商管理员,2-代理商,3-客服,4-开发者>>
    user_level : TINYINT <<0-基础,1-高级,2-VIP,3-企业版>>
    nickname : VARCHAR(50)
    real_name_encrypted : VARCHAR(255) <<真实姓名加密存储>>
    avatar_url : VARCHAR(500)
    gender : TINYINT <<0-未知,1-男性,2-女性,3-其他>>
    birth_date : DATE
    phone_hash : VARCHAR(64) <<手机号哈希>>
    phone_verified : TINYINT <<0-未验证,1-已验证>>
    address_encrypted : JSON <<地址信息加密>>
    account_status : TINYINT <<0-待激活,1-活跃,2-暂停,3-已删除>>
    suspension_reason : VARCHAR(200)
    activation_token : VARCHAR(64)
    activation_expires_at : DATETIME
    subscription_type : VARCHAR(20) <<FREE/BASIC/PRO/ENTERPRISE>>
    subscription_expires_at : DATETIME
    last_login_at : DATETIME
    login_count : INT
    failed_login_attempts : INT
    locked_until : DATETIME
    mfa_enabled : TINYINT <<0-未启用,1-已启用>>
    mfa_secret : VARCHAR(64) <<TOTP密钥>>
    preferences : JSON <<用户偏好设置>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 权限管理层
package "PermissionLayer" {
  entity dtf_tenant_role {
    * id : BIGINT <<PK>>
    * tenant_id : BIGINT <<FK>>
    * role_code : VARCHAR(50) <<UK,租户内唯一>>
    --
    role_name : VARCHAR(100)
    role_description : TEXT
    permissions : JSON <<权限列表>>
    data_scope : JSON <<数据访问范围>>
    role_type : TINYINT <<0-系统角色,1-业务角色,2-自定义角色>>
    is_system_role : TINYINT <<0-否,1-是>>
    parent_role_id : BIGINT <<FK,支持角色继承>>
    role_status : TINYINT <<0-草稿,1-启用,2-禁用,3-已删除>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_tenant_user_role {
    * id : BIGINT <<PK>>
    * user_id : BIGINT <<FK>>
    * role_id : BIGINT <<FK>>
    * tenant_id : BIGINT <<FK>>
    --
    assigned_at : DATETIME
    assigned_by : BIGINT <<FK,分配者用户ID>>
    expires_at : DATETIME <<权限过期时间>>
    assignment_status : TINYINT <<0-待生效,1-生效,2-暂停,3-撤销>>
    assignment_reason : VARCHAR(200)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 设备管理层
package "DeviceManagementLayer" {
  entity dtf_device {
    * id : BIGINT <<PK>>
    * serial_number : VARCHAR(100) <<UK>>
    owner_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    device_name : VARCHAR(100)
    device_model : VARCHAR(100)
    manufacturer : VARCHAR(100)
    firmware_version : VARCHAR(50)
    hardware_version : VARCHAR(50)
    device_type : TINYINT <<0-激光雕刻机,1-3D打印机,2-切割机,3-其他>>
    capabilities : JSON <<设备能力配置>>
    specifications : JSON <<设备规格参数>>
    location_info : JSON <<位置信息>>
    network_config : JSON <<网络配置>>
    status : TINYINT <<0-离线,1-在线,2-忙碌,3-维护,4-故障>>
    health_score : DECIMAL(3,2) <<健康度评分0-1>>
    last_heartbeat : DATETIME
    registered_at : DATETIME
    last_active_at : DATETIME
    warranty_expires_at : DATE
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_device_usage {
    * id : BIGINT <<PK>>
    device_id : BIGINT <<FK>>
    user_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    session_id : VARCHAR(64) <<会话标识>>
    start_time : DATETIME
    end_time : DATETIME
    duration_minutes : INT <<使用时长(分钟)>>
    operation_type : VARCHAR(50) <<操作类型:雕刻/切割/打印等>>
    project_name : VARCHAR(200)
    material_type : VARCHAR(100)
    material_used : JSON <<材料使用详情>>
    energy_consumed : DECIMAL(10,2) <<能耗(kWh)>>
    operation_result : TINYINT <<0-成功,1-失败,2-中断,3-取消>>
    error_codes : JSON <<错误代码列表>>
    performance_metrics : JSON <<性能指标>>
    created_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 内容管理层
package "ContentManagementLayer" {
  entity dtf_gallery_item {
    * id : BIGINT <<PK>>
    creator_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    title : VARCHAR(200)
    description : TEXT
    category : TINYINT <<0-海报,1-横幅,2-标志,3-卡片,4-模板,5-其他>>
    tags : JSON <<标签列表>>
    difficulty_level : TINYINT <<1-新手,2-进阶,3-专业,4-资深,5-大师>>
    estimated_time : INT <<预估制作时间(分钟)>>
    material_requirements : JSON <<材料需求>>
    price : DECIMAL(10,2) <<价格(美分)>>
    currency : VARCHAR(3) <<货币代码>>
    license_type : TINYINT <<0-免费,1-付费,2-订阅,3-企业版>>
    copyright_info : JSON <<版权信息>>
    download_count : INT <<下载次数>>
    like_count : INT <<点赞数>>
    view_count : INT <<查看次数>>
    rating_average : DECIMAL(3,2) <<平均评分>>
    rating_count : INT <<评分人数>>
    status : TINYINT <<0-草稿,1-审核中,2-已发布,3-已下架,4-已删除>>
    featured : TINYINT <<0-否,1-是,是否精选>>
    published_at : DATETIME
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_content_file {
    * id : BIGINT <<PK>>
    gallery_item_id : BIGINT <<FK>>
    --
    file_name : VARCHAR(255)
    file_type : VARCHAR(50) <<文件类型:SVG/PNG/JPG/PDF等>>
    file_size : BIGINT <<文件大小(字节)>>
    file_path : VARCHAR(500)
    storage_url : VARCHAR(500)
    thumbnail_url : VARCHAR(500)
    preview_url : VARCHAR(500)
    file_hash : VARCHAR(64) <<文件哈希值>>
    resolution : VARCHAR(20) <<分辨率>>
    color_mode : VARCHAR(20) <<颜色模式:RGB/CMYK等>>
    dpi : INT <<分辨率DPI>>
    metadata : JSON <<文件元数据>>
    is_primary : TINYINT <<0-否,1-是,是否主文件>>
    sort_order : INT <<排序顺序>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_community_post {
    * id : BIGINT <<PK>>
    author_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    title : VARCHAR(200)
    content : TEXT
    post_type : TINYINT <<0-作品展示,1-教程分享,2-问题求助,3-讨论交流,4-公告>>
    category : TINYINT <<0-设计技巧,1-设备使用,2-材料分享,3-故障排除,4-其他>>
    tags : JSON <<标签列表>>
    attachments : JSON <<附件列表>>
    view_count : INT <<查看次数>>
    like_count : INT <<点赞数>>
    comment_count : INT <<评论数>>
    share_count : INT <<分享次数>>
    is_pinned : TINYINT <<0-否,1-是,是否置顶>>
    is_featured : TINYINT <<0-否,1-是,是否精选>>
    status : TINYINT <<0-草稿,1-已发布,2-已删除,3-已屏蔽>>
    published_at : DATETIME
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 商务管理层
package "CommerceLayer" {
  entity dtf_product {
    * id : BIGINT <<PK>>
    seller_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    product_name : VARCHAR(200)
    product_code : VARCHAR(100) <<UK,商品编码>>
    description : TEXT
    category : TINYINT <<0-设备,1-材料,2-配件,3-软件,4-服务>>
    brand : VARCHAR(100)
    model : VARCHAR(100)
    specifications : JSON <<商品规格>>
    features : JSON <<功能特性>>
    price : DECIMAL(10,2) <<价格(美分)>>
    currency : VARCHAR(3)
    cost_price : DECIMAL(10,2) <<成本价>>
    market_price : DECIMAL(10,2) <<市场价>>
    discount_info : JSON <<折扣信息>>
    inventory_quantity : INT <<库存数量>>
    min_order_quantity : INT <<最小订购量>>
    max_order_quantity : INT <<最大订购量>>
    weight : DECIMAL(8,2) <<重量(kg)>>
    dimensions : JSON <<尺寸信息>>
    shipping_info : JSON <<配送信息>>
    warranty_period : INT <<保修期(月)>>
    images : JSON <<商品图片列表>>
    videos : JSON <<商品视频列表>>
    documents : JSON <<商品文档列表>>
    seo_keywords : JSON <<SEO关键词>>
    status : TINYINT <<0-草稿,1-在售,2-缺货,3-下架,4-停产>>
    is_featured : TINYINT <<0-否,1-是,是否推荐>>
    sort_order : INT
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_order {
    * id : BIGINT <<PK>>
    * order_number : VARCHAR(50) <<UK>>
    buyer_id : BIGINT <<FK>>
    seller_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    order_type : TINYINT <<0-普通订单,1-预订单,2-定制订单,3-批量订单>>
    subtotal_amount : DECIMAL(10,2) <<商品小计>>
    shipping_amount : DECIMAL(10,2) <<运费>>
    tax_amount : DECIMAL(10,2) <<税费>>
    discount_amount : DECIMAL(10,2) <<折扣金额>>
    total_amount : DECIMAL(10,2) <<订单总金额>>
    currency : VARCHAR(3)
    payment_method : VARCHAR(50)
    payment_status : TINYINT <<0-待支付,1-已支付,2-部分支付,3-已退款,4-支付失败>>
    shipping_address : JSON <<收货地址>>
    billing_address : JSON <<账单地址>>
    shipping_method : VARCHAR(50)
    tracking_number : VARCHAR(100)
    estimated_delivery : DATE
    actual_delivery : DATE
    status : TINYINT <<0-待支付,1-待发货,2-已发货,3-已送达,4-已完成,5-已取消,6-已退货>>
    cancel_reason : VARCHAR(200)
    notes : TEXT <<订单备注>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_order_item {
    * id : BIGINT <<PK>>
    order_id : BIGINT <<FK>>
    product_id : BIGINT <<FK>>
    --
    product_name : VARCHAR(200) <<下单时商品名称>>
    product_code : VARCHAR(100) <<下单时商品编码>>
    specifications : JSON <<选择的规格>>
    quantity : INT <<购买数量>>
    unit_price : DECIMAL(10,2) <<单价>>
    total_price : DECIMAL(10,2) <<小计>>
    discount_amount : DECIMAL(10,2) <<单项折扣>>
    customization : JSON <<定制化信息>>
    delivery_status : TINYINT <<0-待发货,1-已发货,2-已送达,3-已退货>>
    created_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_wallet_account {
    * id : BIGINT <<PK>>
    * user_id : BIGINT <<FK,UK>>
    tenant_id : BIGINT <<FK>>
    --
    account_type : TINYINT <<0-现金账户,1-积分账户,2-佣金账户>>
    balance : DECIMAL(15,2) <<账户余额>>
    frozen_balance : DECIMAL(15,2) <<冻结余额>>
    currency : VARCHAR(3)
    daily_limit : DECIMAL(10,2) <<日限额>>
    monthly_limit : DECIMAL(10,2) <<月限额>>
    total_income : DECIMAL(15,2) <<总收入>>
    total_expense : DECIMAL(15,2) <<总支出>>
    last_transaction_at : DATETIME
    status : TINYINT <<0-正常,1-冻结,2-限制,3-注销>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_transaction {
    * id : BIGINT <<PK>>
    * transaction_number : VARCHAR(50) <<UK>>
    wallet_id : BIGINT <<FK>>
    --
    transaction_type : TINYINT <<0-充值,1-消费,2-退款,3-转账,4-佣金,5-提现>>
    amount : DECIMAL(15,2) <<交易金额,正数收入负数支出>>
    balance_before : DECIMAL(15,2) <<交易前余额>>
    balance_after : DECIMAL(15,2) <<交易后余额>>
    currency : VARCHAR(3)
    description : VARCHAR(500)
    related_order_id : BIGINT <<FK,关联订单>>
    related_user_id : BIGINT <<FK,关联用户(转账等)>>
    payment_method : VARCHAR(50)
    external_transaction_id : VARCHAR(100) <<外部交易ID>>
    status : TINYINT <<0-处理中,1-成功,2-失败,3-已撤销>>
    processed_at : DATETIME
    created_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' AI服务层
package "AIServiceLayer" {
  entity dtf_ai_tool {
    * id : BIGINT <<PK>>
    * tool_code : VARCHAR(50) <<UK>>
    tenant_id : BIGINT <<FK>>
    --
    tool_name : VARCHAR(100)
    tool_description : TEXT
    tool_type : TINYINT <<0-图像处理,1-背景移除,2-风格转换,3-智能裁剪,4-其他>>
    provider : VARCHAR(50) <<AI服务提供商>>
    api_endpoint : VARCHAR(200)
    model_version : VARCHAR(50)
    input_formats : JSON <<支持的输入格式>>
    output_formats : JSON <<支持的输出格式>>
    parameters : JSON <<工具参数配置>>
    pricing_model : TINYINT <<0-免费,1-按次计费,2-按时长计费,3-包月>>
    price_per_use : DECIMAL(8,4) <<单次使用价格>>
    free_quota : INT <<免费配额>>
    rate_limit : JSON <<使用限制>>
    quality_settings : JSON <<质量设置>>
    status : TINYINT <<0-开发中,1-测试,2-上线,3-维护,4-下线>>
    is_featured : TINYINT <<0-否,1-是>>
    sort_order : INT
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_ai_processing_task {
    * id : BIGINT <<PK>>
    * task_id : VARCHAR(64) <<UK>>
    user_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    tool_id : BIGINT <<FK>>
    --
    task_name : VARCHAR(200)
    input_files : JSON <<输入文件列表>>
    output_files : JSON <<输出文件列表>>
    parameters : JSON <<处理参数>>
    priority : TINYINT <<1-低,2-普通,3-高,4-紧急>>
    estimated_duration : INT <<预估处理时间(秒)>>
    actual_duration : INT <<实际处理时间(秒)>>
    progress : TINYINT <<处理进度0-100>>
    status : TINYINT <<0-排队,1-处理中,2-成功,3-失败,4-取消>>
    error_message : TEXT
    cost : DECIMAL(8,4) <<处理成本>>
    quality_score : DECIMAL(3,2) <<质量评分>>
    started_at : DATETIME
    completed_at : DATETIME
    created_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 存储管理层
package "StorageLayer" {
  entity dtf_storage_space {
    * id : BIGINT <<PK>>
    * user_id : BIGINT <<FK,UK>>
    tenant_id : BIGINT <<FK>>
    --
    total_quota : BIGINT <<总配额(字节)>>
    used_space : BIGINT <<已使用空间(字节)>>
    available_space : BIGINT <<可用空间(字节)>>
    file_count : INT <<文件数量>>
    folder_count : INT <<文件夹数量>>
    last_cleanup_at : DATETIME <<最后清理时间>>
    auto_cleanup_enabled : TINYINT <<0-否,1-是>>
    backup_enabled : TINYINT <<0-否,1-是>>
    sync_enabled : TINYINT <<0-否,1-是>>
    status : TINYINT <<0-正常,1-超限,2-冻结,3-清理中>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_file_object {
    * id : BIGINT <<PK>>
    storage_space_id : BIGINT <<FK>>
    --
    file_name : VARCHAR(255)
    file_path : VARCHAR(500)
    file_type : VARCHAR(50)
    file_size : BIGINT <<文件大小(字节)>>
    mime_type : VARCHAR(100)
    storage_provider : VARCHAR(50) <<存储提供商:AWS/阿里云等>>
    storage_region : VARCHAR(50)
    storage_url : VARCHAR(500)
    cdn_url : VARCHAR(500)
    file_hash : VARCHAR(64) <<文件哈希值>>
    checksum : VARCHAR(64) <<校验和>>
    encryption_key : VARCHAR(128) <<加密密钥>>
    metadata : JSON <<文件元数据>>
    tags : JSON <<文件标签>>
    access_level : TINYINT <<0-私有,1-租户内,2-公开>>
    download_count : INT
    last_accessed_at : DATETIME
    expires_at : DATETIME <<过期时间>>
    is_backup : TINYINT <<0-否,1-是>>
    backup_source_id : BIGINT <<FK,备份源文件ID>>
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 合规管理层
package "ComplianceLayer" {
  entity dtf_data_processing_consent {
    * id : BIGINT <<PK>>
    user_id : BIGINT <<FK>>
    tenant_id : BIGINT <<FK>>
    --
    processing_purpose : TINYINT <<0-服务提供,1-营销,2-分析,3-安全,4-法律合规>>
    legal_basis : TINYINT <<0-同意,1-合同,2-法律义务,3-重要利益,4-公共任务,5-合法利益>>
    consent_status : TINYINT <<0-已同意,1-已撤回,2-已过期>>
    consent_timestamp : DATETIME
    withdrawal_timestamp : DATETIME
    consent_method : TINYINT <<0-网页,1-移动端,2-邮件,3-电话,4-其他>>
    consent_version : VARCHAR(20)
    data_categories : JSON <<数据类别>>
    retention_period : INT <<保留期限(天)>>
    third_party_sharing : TINYINT <<0-否,1-是>>
    cross_border_transfer : TINYINT <<0-否,1-是>>
    ip_address_hash : VARCHAR(64)
    user_agent_hash : VARCHAR(64)
    created_at : DATETIME
    updated_at : DATETIME
    version : INT
    deleted : TINYINT
  }

  entity dtf_audit_log {
    * id : BIGINT <<PK>>
    tenant_id : BIGINT <<FK>>
    user_id : BIGINT <<FK>>
    --
    operation_type_code : TINYINT <<0-CREATE,1-UPDATE,2-DELETE,3-READ,4-LOGIN,5-LOGOUT,6-EXPORT,7-IMPORT>>
    operation_type : VARCHAR(50) <<操作类型描述>>
    resource_type_code : TINYINT <<0-USER,1-TENANT,2-ROLE,3-PERMISSION,4-DATA,5-SYSTEM>>
    resource_type : VARCHAR(50) <<资源类型描述>>
    resource_id : VARCHAR(100)
    operation_details : JSON <<操作详情>>
    ip_address_hash : VARCHAR(64) <<IP地址SHA256哈希>>
    ip_address_masked : VARCHAR(15) <<IP地址脱敏版本>>
    user_agent_hash : VARCHAR(64) <<用户代理SHA256哈希>>
    user_agent_summary : VARCHAR(100) <<用户代理摘要>>
    request_id : VARCHAR(64)
    session_id : VARCHAR(64)
    operation_result : TINYINT <<0-SUCCESS,1-FAILURE,2-PARTIAL>>
    error_message_hash : VARCHAR(64)
    error_message_encrypted : TEXT <<错误信息加密存储>>
    risk_score : DECIMAL(3,2) <<风险评分>>
    compliance_flags : JSON <<合规标记>>
    created_at : DATETIME
    version : INT
    deleted : TINYINT
  }
}

' 核心关系定义
dtf_global_identity ||--o{ dtf_tenant_user_account : "global_user_id"
dtf_deployment_instance ||--o{ dtf_tenant : "deployment_instance_id"
dtf_tenant ||--o{ dtf_tenant_user_account : "tenant_id"
dtf_tenant ||--o{ dtf_tenant_role : "tenant_id"
dtf_tenant_user_account ||--o{ dtf_tenant_user_role : "user_id"
dtf_tenant_role ||--o{ dtf_tenant_user_role : "role_id"

dtf_tenant_user_account ||--o{ dtf_device : "owner_id"
dtf_device ||--o{ dtf_device_usage : "device_id"
dtf_tenant_user_account ||--o{ dtf_device_usage : "user_id"

dtf_tenant_user_account ||--o{ dtf_gallery_item : "creator_id"
dtf_gallery_item ||--o{ dtf_content_file : "gallery_item_id"
dtf_tenant_user_account ||--o{ dtf_community_post : "author_id"

dtf_tenant_user_account ||--o{ dtf_product : "seller_id"
dtf_tenant_user_account ||--o{ dtf_order : "buyer_id"
dtf_tenant_user_account ||--o{ dtf_order : "seller_id"
dtf_order ||--o{ dtf_order_item : "order_id"
dtf_product ||--o{ dtf_order_item : "product_id"

dtf_tenant_user_account ||--|| dtf_wallet_account : "user_id"
dtf_wallet_account ||--o{ dtf_transaction : "wallet_id"
dtf_order ||--o{ dtf_transaction : "related_order_id"

dtf_tenant_user_account ||--o{ dtf_ai_processing_task : "user_id"
dtf_ai_tool ||--o{ dtf_ai_processing_task : "tool_id"

dtf_tenant_user_account ||--|| dtf_storage_space : "user_id"
dtf_storage_space ||--o{ dtf_file_object : "storage_space_id"

dtf_tenant_user_account ||--o{ dtf_data_processing_consent : "user_id"
dtf_tenant_user_account ||--o{ dtf_audit_log : "user_id"

' 层级关系
dtf_tenant ||--o{ dtf_tenant : "parent_tenant_id"
dtf_tenant_role ||--o{ dtf_tenant_role : "parent_role_id"
dtf_file_object ||--o{ dtf_file_object : "backup_source_id"

@enduml
```

### 6.2 数据库设计原则

#### 6.2.1 字段设计原则

```yaml
数值编码优化:
  用户类型: 0-官方,1-厂商,2-消费者,3-代理
  账户状态: 0-待激活,1-活跃,2-暂停,3-已删除
  设备状态: 0-离线,1-在线,2-忙碌,3-维护,4-故障
  订单状态: 0-待支付,1-待发货,2-已发货,3-已送达,4-已完成,5-已取消,6-已退货
  交易类型: 0-充值,1-消费,2-退款,3-转账,4-佣金,5-提现

隐私保护设计:
  邮箱哈希: email_hash VARCHAR(64) - SHA256哈希存储
  手机哈希: phone_hash VARCHAR(64) - 手机号哈希存储
  真实姓名: real_name_encrypted VARCHAR(255) - 加密存储
  地址信息: address_encrypted JSON - 加密JSON存储
  IP地址: ip_address_hash VARCHAR(64) - IP地址哈希

性能优化设计:
  主键: BIGINT自增，支持大数据量
  外键: 明确标注FK关系，建立索引
  唯一键: UK标识，保证数据唯一性
  JSON字段: 存储复杂配置和扩展信息
  索引策略: 查询频繁字段建立组合索引
```

#### 6.2.2 关系设计原则

```yaml
核心关系链:
  全局身份链: dtf_global_identity -> dtf_tenant_user_account
  租户管理链: dtf_deployment_instance -> dtf_tenant -> dtf_tenant_user_account
  权限控制链: dtf_tenant_role -> dtf_tenant_user_role -> dtf_tenant_user_account
  业务数据链: dtf_tenant_user_account -> 各业务表

数据隔离策略:
  租户级隔离: 所有业务表包含tenant_id字段
  用户级隔离: 用户相关表通过user_id关联
  全局身份: global_user_id实现跨租户身份统一

引用完整性:
  外键约束: 保证数据引用完整性
  级联策略: 删除时的级联处理规则
  软删除: deleted字段实现逻辑删除
```

#### 6.2.3 合规性设计原则

```yaml
GDPR合规:
  数据最小化: 只收集必要的业务数据
  同意管理: dtf_data_processing_consent表记录用户同意
  数据主体权利: 支持访问、删除、可携带等权利
  数据保留: retention_period字段控制数据保留期限

CCPA合规:
  隐私保护: 敏感数据加密和哈希存储
  数据透明: 详细记录数据处理目的和法律依据
  选择退出: 支持用户撤回同意和删除数据

SOX合规:
  审计追踪: dtf_audit_log表完整记录操作日志
  数据完整性: 版本控制和校验机制
  访问控制: 详细的权限管理和角色控制
```

### 6.3 表结构详细说明

#### 6.3.1 核心身份表

```yaml
dtf_global_identity:
  作用: 全局用户身份管理，跨租户唯一标识
  特点: 支持风险评分、合规管理、隐私保护
  索引: global_user_id(UK), email_hash, identity_type+global_status

dtf_tenant:
  作用: 租户管理，支持层级结构和代理商体系
  特点: 丰富的配置选项，支持品牌定制和功能开关
  索引: tenant_code(UK), deployment_instance_id, parent_tenant_id

dtf_tenant_user_account:
  作用: 租户内用户账户，支持同邮箱多租户注册
  特点: 完整的用户信息和安全控制
  索引: global_user_id+tenant_id, email+tenant_id(UK)
```

#### 6.3.2 业务核心表

```yaml
设备管理:
  dtf_device: 设备基础信息和状态管理
  dtf_device_usage: 详细的设备使用记录和分析数据

内容管理:
  dtf_gallery_item: 图库作品信息和商业化配置
  dtf_content_file: 多格式文件支持和CDN分发
  dtf_community_post: 社区内容和用户互动

商务管理:
  dtf_product: 商品信息和库存管理
  dtf_order: 订单全生命周期管理
  dtf_wallet_account: 多币种钱包账户
  dtf_transaction: 完整的交易记录和对账

AI服务:
  dtf_ai_tool: AI工具配置和定价模型
  dtf_ai_processing_task: AI处理任务和质量评估

存储管理:
  dtf_storage_space: 用户存储配额和使用统计
  dtf_file_object: 文件对象管理和CDN分发
```

#### 6.3.3 合规管理表

```yaml
dtf_data_processing_consent:
  作用: GDPR/CCPA合规的用户同意管理
  特点: 详细记录同意状态、撤回时间、数据类别
  合规: 支持数据主体权利和跨境传输控制

dtf_audit_log:
  作用: SOX合规的完整审计日志
  特点: 操作记录、风险评分、合规标记
  安全: IP地址哈希、错误信息加密存储
```

### 6.4 数据库部署架构

#### 6.4.1 多租户数据隔离

```yaml
隔离级别:
  - 数据库级: 大型租户独立数据库
  - Schema级: 中型租户独立Schema
  - 表级: 小型租户共享表+tenant_id

部署模式:
  - SaaS模式: 共享基础设施
  - 私有云: 租户独立部署
  - 混合云: 灵活组合部署
```

#### 6.4.2 全球化部署

```yaml
地区部署:
  - 美国: 主要数据中心
  - 欧洲: GDPR合规部署
  - 亚太: 本地化服务

数据同步:
  - 主从复制: 实时数据同步
  - 跨地区备份: 灾难恢复
  - 数据本地化: 合规要求
```

#### 6.4.3 性能优化

```yaml
索引策略:
  - 主键索引: BIGINT自增主键
  - 唯一索引: 业务唯一约束
  - 复合索引: 多字段查询优化
  - JSON索引: 生成列索引

分区策略:
  - 时间分区: 按月分区大表
  - 租户分区: 按tenant_id分区
  - 地区分区: 按region分区

缓存策略:
  - Redis集群: 分布式缓存
  - 本地缓存: 应用级缓存
  - CDN缓存: 静态资源缓存
```

## 🏗️ 7. 微服务架构设计

<!-- ### 7.1 整体架构概览 -->

基于DDD领域模型，采用微服务架构，结合AWS云原生服务:

```plantuml
@startuml DTF_Microservices_Architecture

title DTF玩图平台微服务架构 - AWS云原生部署

package "客户端层" {
  component [PC桌面端] as PC
  component [移动端APP] as Mobile  
  component [Web管理端] as WebAdmin
  component [设备厂商端] as VendorPortal
  component [代理商端] as AgentPortal
}

package "API网关层" {
  component [AWS API Gateway] as APIGateway
  component [AWS CloudFront] as CDN
}

package "核心业务微服务" {
  package "账户域服务" {
    component [用户服务] as UserService
    component [认证服务] as AuthService
    component [租户服务] as TenantService
  }
  
  package "设备域服务" {
    component [设备服务] as DeviceService
    component [监控服务] as MonitorService
  }
  
  package "内容域服务" {
    component [图库服务] as GalleryService
    component [社区服务] as CommunityService
    component [内容服务] as ContentService
  }
  
  package "商务域服务" {
    component [商城服务] as MallService
    component [订单服务] as OrderService
    component [支付服务] as PaymentService
    component [钱包服务] as WalletService
  }
  
  package "AI域服务" {
    component [AI服务] as AIService
    component [AWS SageMaker] as AIModel
    component [AWS Lambda] as AIProcessor
  }
  
  package "存储域服务" {
    component [存储服务] as StorageService
    component [AWS S3] as S3Storage
  }
}

package "基础设施层" {
  package "数据存储" {
    component [AWS RDS MySQL] as MainDB
    component [AWS RDS ReadReplica] as ReadDB
    component [AWS ElastiCache] as Redis
  }
  
  package "消息队列" {
    component [AWS Kinesis] as EventStream
    component [AWS SQS] as MessageQueue
    component [AWS SNS] as Notification
  }
  
  package "监控日志" {
    component [AWS CloudWatch] as CloudWatch
    component [AWS X-Ray] as XRay
    component [SkyWalking] as SkyWalking
  }
}

package "合规安全层" {
  component [AWS KMS] as Encryption
  component [AWS CloudTrail] as AuditLog
  component [数据隐私保护] as Privacy
  component [AWS IAM] as AccessControl
}

PC --> APIGateway
Mobile --> APIGateway
WebAdmin --> APIGateway
VendorPortal --> APIGateway
AgentPortal --> APIGateway

APIGateway --> CDN
APIGateway --> AuthService

AuthService --> UserService
AuthService --> TenantService
UserService --> TenantService
DeviceService --> MonitorService
GalleryService --> ContentService
MallService --> OrderService
OrderService --> PaymentService
PaymentService --> WalletService

AIService --> AIModel
AIService --> AIProcessor
StorageService --> S3Storage

UserService --> MainDB
TenantService --> MainDB
DeviceService --> MainDB
GalleryService --> MainDB
MallService --> MainDB

UserService --> Redis
AuthService --> Redis
GalleryService --> Redis

AIService --> EventStream
OrderService --> MessageQueue
PaymentService --> Notification

CloudWatch --> UserService
CloudWatch --> DeviceService
CloudWatch --> GalleryService
XRay --> AIService
SkyWalking --> MallService

Encryption --> MainDB
AuditLog --> CloudWatch
Privacy --> UserService
AccessControl --> AuthService

@enduml
```

### 7.1 整体架构概览

基于DDD领域模型，采用微服务架构，结合AWS云原生服务:

```plantuml
@startuml DTF_Microservices_Architecture

title DTF玩图平台微服务架构 - AWS云原生部署

package "客户端层" {
  component [PC桌面端] as PC
  component [移动端APP] as Mobile  
  component [Web管理端] as WebAdmin
  component [设备厂商端] as VendorPortal
  component [代理商端] as AgentPortal
}

package "API网关层" {
  component [AWS API Gateway] as APIGateway
  component [AWS CloudFront] as CDN
}

package "核心业务微服务" {
  package "账户域服务" {
    component [用户服务] as UserService
    component [认证服务] as AuthService
    component [租户服务] as TenantService
  }
  
  package "设备域服务" {
    component [设备服务] as DeviceService
    component [监控服务] as MonitorService
  }
  
  package "内容域服务" {
    component [图库服务] as GalleryService
    component [社区服务] as CommunityService
    component [内容服务] as ContentService
  }
  
  package "商务域服务" {
    component [商城服务] as MallService
    component [订单服务] as OrderService
    component [支付服务] as PaymentService
    component [钱包服务] as WalletService
  }
  
  package "AI域服务" {
    component [AI服务] as AIService
    component [AWS SageMaker] as AIModel
    component [AWS Lambda] as AIProcessor
  }
  
  package "存储域服务" {
    component [存储服务] as StorageService
    component [AWS S3] as S3Storage
  }
}

package "基础设施层" {
  package "数据存储" {
    component [AWS RDS MySQL] as MainDB
    component [AWS RDS ReadReplica] as ReadDB
    component [AWS ElastiCache] as Redis
  }
  
  package "消息队列" {
    component [AWS Kinesis] as EventStream
    component [AWS SQS] as MessageQueue
    component [AWS SNS] as Notification
  }
  
  package "监控日志" {
    component [AWS CloudWatch] as CloudWatch
    component [AWS X-Ray] as XRay
    component [SkyWalking] as SkyWalking
  }
}

package "合规安全层" {
  component [AWS KMS] as Encryption
  component [AWS CloudTrail] as AuditLog
  component [数据隐私保护] as Privacy
  component [AWS IAM] as AccessControl
}

PC --> APIGateway
Mobile --> APIGateway
WebAdmin --> APIGateway
VendorPortal --> APIGateway
AgentPortal --> APIGateway

APIGateway --> CDN
APIGateway --> AuthService

AuthService --> UserService
AuthService --> TenantService
UserService --> TenantService
DeviceService --> MonitorService
GalleryService --> ContentService
MallService --> OrderService
OrderService --> PaymentService
PaymentService --> WalletService

AIService --> AIModel
AIService --> AIProcessor
StorageService --> S3Storage

UserService --> MainDB
TenantService --> MainDB
DeviceService --> MainDB
GalleryService --> MainDB
MallService --> MainDB

UserService --> Redis
AuthService --> Redis
GalleryService --> Redis

AIService --> EventStream
OrderService --> MessageQueue
PaymentService --> Notification

CloudWatch --> UserService
CloudWatch --> DeviceService
CloudWatch --> GalleryService
XRay --> AIService
SkyWalking --> MallService

Encryption --> MainDB
AuditLog --> CloudWatch
Privacy --> UserService
AccessControl --> AuthService

@enduml
```

#### 7.1.1 DTF微服务模块架构

基于现有的dtf-tool项目结构，展示完整的微服务模块划分:

```plantuml
@startuml DTF_Tool_Microservices_Architecture

title DTF-Tool微服务模块架构 - 基于Spring Boot 3.2.6

package "项目根模块" {
  component [dtf-tool-parent] as Parent
  
  note right of Parent
  Maven父模块
  - 依赖版本管理
  - 公共配置
  - 构建配置
  end note
}

package "公共模块层" {
  component [dtf-tool-common] as Common
  
  note right of Common
  公共基础模块
  - 通用工具类
  - 异常处理框架
  - 响应结果封装
  - 自定义验证注解
  - 常量和枚举定义
  end note
}

package "网关服务层" {
  component [dtf-tool-gateway:8080] as Gateway
  
  note right of Gateway
  Spring Cloud Gateway
  - 统一API入口
  - 认证授权
  - 限流熔断
  - 路由转发
  - 跨域处理
  end note
}

package "业务服务层" {
  component [dtf-tool-tenant:8081] as TenantService
  
  note right of TenantService
  租户管理微服务
  - DDD六边形架构
  - 多租户数据隔离
  - Google OAuth2集成
  - JWT+Redis会话
  - 异步审计日志
  end note
}

package "技术栈支撑" {
  package "服务治理" {
    component [Nacos注册中心] as NacosRegistry
    component [Nacos配置中心] as NacosConfig
    component [Dubbo RPC] as DubboRPC
  }
  
  package "数据存储" {
    component [MySQL 8.0] as MySQL
    component [Redis 7.0] as Redis
    component [MyBatis Plus] as MyBatisPlus
  }
  
  package "监控链路" {
    component [SkyWalking] as SkyWalking
    component [Prometheus] as Prometheus
    component [Grafana] as Grafana
  }
}

' 依赖关系
Parent --> Common : 依赖管理
Parent --> Gateway : 依赖管理
Parent --> TenantService : 依赖管理

Gateway --> Common : 公共依赖
TenantService --> Common : 公共依赖

Gateway --> TenantService : 路由转发
Gateway --> NacosRegistry : 服务发现
TenantService --> NacosRegistry : 服务注册
TenantService --> NacosConfig : 配置获取

TenantService --> MySQL : 数据持久化
TenantService --> Redis : 缓存存储
TenantService --> DubboRPC : RPC调用

TenantService --> SkyWalking : 链路追踪
Gateway --> Prometheus : 指标收集

@enduml
```

#### 7.1.2 技术选型与版本规范

基于dtf-tool-parent项目的pom.xml配置，整理完整的技术栈:

```yaml
核心框架版本:
  Java版本: 17
  Spring Boot: 3.2.6
  Spring Cloud: 2023.0.2
  Spring Cloud Alibaba: 2022.0.0.0

微服务治理:
  服务注册发现: 
    - Nacos: 2.3.2
    - Spring Cloud Starter Alibaba Nacos Discovery: 2022.0.0.0
  
  配置管理:
    - Nacos Config: 2.3.2
    - Spring Cloud Starter Alibaba Nacos Config: 2022.0.0.0
  
  RPC框架:
    - Apache Dubbo: 3.2.8
    - Dubbo Spring Boot Starter: 3.2.8
  
  API网关:
    - Spring Cloud Gateway: 4.0.6
    - Spring Cloud Starter Gateway: 2023.0.2

数据访问层:
  数据库:
    - MySQL: 8.0.33
    - MySQL Connector: 8.0.33
  
  ORM框架:
    - MyBatis Plus: 3.5.5
    - MyBatis Plus Boot Starter: 3.5.5
  
  数据库连接池:
    - HikariCP: 5.0.1 (Spring Boot内置)
  
  缓存:
    - Redis: 7.0
    - Spring Boot Starter Data Redis: 3.2.6
    - Redisson: 3.21.3

安全认证:
  认证授权:
    - Spring Security: 6.2.4
    - Spring Security OAuth2: 6.2.4
    - JWT: 0.11.5
  
  Google OAuth2:
    - Google OAuth2 Client: 1.34.1
    - Spring Security OAuth2 Client: 6.2.4

消息队列:
  消息中间件:
    - RocketMQ: 5.1.4
    - RocketMQ Spring Boot Starter: 2.2.3
  
  事件驱动:
    - Spring Cloud Stream: 4.0.4
    - Spring Cloud Bus: 4.0.4

监控链路:
  链路追踪:
    - SkyWalking: 9.0.0
    - SkyWalking Agent: 9.0.0
  
  指标监控:
    - Micrometer: 1.12.6
    - Prometheus: 1.12.6
    - Spring Boot Actuator: 3.2.6
  
  日志管理:
    - Logback: 1.4.14
    - SLF4J: 2.0.13

工具库:
  通用工具:
    - Hutool: 5.8.25
    - Apache Commons Lang3: 3.12.0
    - Apache Commons Collections4: 4.4
    - Apache Commons IO: 2.11.0
  
  JSON处理:
    - Jackson: 2.15.4
    - FastJSON2: 2.0.43
  
  函数式编程:
    - Vavr: 0.10.4
  
  Excel处理:
    - EasyExcel: 3.3.2
  
  API文档:
    - Knife4j: 4.3.0
    - SpringDoc OpenAPI: 2.2.0

测试框架:
  单元测试:
    - JUnit 5: 5.10.2
    - Mockito: 5.3.1
    - Spring Boot Test: 3.2.6
  
  集成测试:
    - TestContainers: 1.19.0
    - H2 Database: 2.2.224

构建工具:
  构建管理:
    - Maven: 3.9.6
    - Maven Compiler Plugin: 3.11.0
    - Maven Surefire Plugin: 3.1.2
  
  代码质量:
    - SpotBugs: 4.7.3
    - Checkstyle: 10.12.1
    - PMD: 6.55.0

容器化部署:
  容器技术:
    - Docker: 24.0.7
    - Docker Compose: 2.21.0
  
  镜像构建:
    - Spring Boot Maven Plugin: 3.2.6
    - Jib Maven Plugin: 3.4.0

云原生支持:
  服务网格:
    - Istio: 1.19.0 (可选)
    - Envoy Proxy: 1.28.0 (可选)
  
  容器编排:
    - Kubernetes: 1.28.0
    - Helm: 3.13.0
```

##### ******* 技术选型优势、健壮性

基于DTF玩图平台的业务特点和技术挑战，结合hoson-pod现有技术栈的经验教训，制定以下技术选型策略:

###### *******.1 核心框架选型分析

**Java版本选择: JDK 17 LTS**

```yaml
选择理由:
  业务驱动:
    - DTF平台需要处理大量图像和AI计算，JDK 17的性能优化关键
    - 全球化部署需要更好的内存管理和GC性能
    - 海外合规要求更强的安全特性支持
  
  技术优势:
    - ZGC垃圾收集器: 适合大内存图像处理场景
    - Vector API预览: AI图像处理性能提升30%+
    - 密封类: 更安全的领域模型设计
    - Pattern Matching: 简化复杂业务逻辑
  
  对比分析:
    vs JDK 21 LTS:
      ⚠️ 过于激进，生态兼容性风险
      ⚠️ 第三方库支持不够成熟
      ✅ JDK 17已足够满足DTF需求
```

**Spring Boot版本选择: 3.2.6**

```yaml
选择理由:
  业务需求匹配:
    - DTF需要原生支持云原生部署(AWS/Azure)
    - 多租户架构需要更强的安全框架
    - AI服务集成需要响应式编程支持
    - 全球化需要更好的可观测性
  
  技术优势:
    - 原生支持GraalVM: AI模型推理性能提升
    - 增强的安全框架: 满足GDPR/CCPA合规
    - 改进的Actuator: 支持复杂监控需求
    - WebFlux成熟: 支持高并发图像处理
  
  版本对比:
    vs Spring Boot 2.7.18 (hoson-pod使用):
      ❌ 不支持JDK 17新特性
      ❌ 安全框架版本过低，合规风险
      ❌ 云原生支持不足
      ❌ 响应式编程能力有限
  
    为何不升级hoson-pod:
      - POD平台业务相对简单，升级收益有限
      - DTF平台技术要求更高，必须使用新版本
      - 两个平台可以并存，逐步统一
```

**Spring Cloud版本选择: 2023.0.2**

```yaml
选择理由:
  DTF特殊需求:
    - 全球多地区部署需要更强的服务发现
    - 设备厂商独立部署需要灵活的配置管理
    - AI服务调用需要智能负载均衡
    - 图像处理需要更好的熔断机制
  
  技术优势:
    - LoadBalancer 4.0: 支持AI服务的智能路由
    - Gateway 4.0: 更强的限流和安全控制
    - Config 4.0: 支持多环境动态配置
    - Circuit Breaker: 适合AI服务的熔断策略
  
  对比分析:
    vs Spring Cloud 2021.0.8 (hoson-pod使用):
      ❌ 不支持Spring Boot 3.x
      ❌ 网关性能不足以支撑图像上传
      ❌ 配置管理不支持多租户场景
      ❌ 监控能力无法满足全球化部署
```

###### *******.2 数据存储技术选型

**数据库选择: MySQL 8.0.33**

```yaml
DTF业务特点分析:
  - 用户数据: 全球分布，需要分区支持
  - 设备数据: 时序特征明显，需要JSON支持
  - 图像元数据: 复杂查询，需要全文索引
  - 订单数据: 强一致性，需要事务支持
  
  MySQL 8.0优势:
    - JSON数据类型: 完美支持设备配置存储
    - 分区表: 支持全球化数据分布
    - 全文索引: 支持图像标签搜索
    - 窗口函数: 支持复杂的数据分析
  
  vs MySQL 8.0.33 (hoson-pod使用):
    ✅ 版本一致，便于运维统一
    ✅ 已验证稳定性和性能
    ✅ 团队熟悉度高，降低风险
```

**ORM框架选择: MyBatis Plus 3.5.5**

```yaml
选择理由:
  DTF复杂查询需求:
    - 多租户数据隔离需要动态SQL
    - 图像搜索需要复杂的联表查询
    - 设备监控需要时序数据聚合
    - 订单分析需要复杂的统计查询
  
  MyBatis Plus优势:
    - 多租户插件: 自动数据隔离
    - 分页插件: 支持大数据量查询
    - 代码生成: 提高开发效率
    - 性能分析: 支持SQL优化
  
  版本升级理由:
    vs MyBatis Plus 3.5.3.1 (hoson-pod使用):
      ✅ 修复多租户插件关键bug
      ✅ 增强JSON字段支持
      ✅ 改进分页性能
      ✅ 兼容Spring Boot 3.x
```

**缓存选择: Redis 7.0 + Redisson 3.21.3**

```yaml
DTF缓存场景分析:
  - 用户会话: 全球分布式会话管理
  - 图像缓存: 大文件分布式缓存
  - AI结果: 计算结果缓存优化
  - 设备状态: 实时状态缓存
  
  Redis 7.0新特性:
    - Functions: 支持复杂的缓存逻辑
    - ACL增强: 支持多租户权限隔离
    - 内存优化: 支持大图像文件缓存
    - 集群改进: 支持全球化部署
  
  Redisson版本选择:
    vs Redisson 3.16.7 (hoson-pod使用):
      ✅ 支持Redis 7.0新特性
      ✅ 修复分布式锁关键问题
      ✅ 改进大文件处理性能
      ✅ 增强多租户支持
```

###### *******.3 微服务治理技术选型

**服务注册发现: Nacos 2.3.2**

```yaml
DTF全球化部署需求:
  - 多地区服务发现
  - 动态配置管理
  - 服务健康检查
  - 灰度发布支持
  
  Nacos 2.3.2优势:
    - 多数据中心: 支持全球化部署
    - 配置加密: 满足合规要求
    - 服务网格: 支持云原生架构
    - 性能优化: 支持大规模集群
  
  升级必要性:
    vs Nacos 2.1.2 (hoson-pod使用):
      ❌ 不支持多数据中心
      ❌ 配置安全性不足
      ❌ 性能无法支撑DTF规模
      ❌ 云原生支持有限
```

**RPC框架: Dubbo 3.2.8**

```yaml
DTF高性能需求:
  - AI服务调用: 需要高性能RPC
  - 图像传输: 需要大文件支持
  - 设备通信: 需要低延迟通信
  - 全球调用: 需要智能路由
  
  Dubbo 3.2.8优势:
    - Triple协议: 支持大文件传输
    - 应用级注册: 支持大规模部署
    - 云原生: 支持Kubernetes部署
    - 多协议: 支持gRPC互操作
  
  版本选择理由:
    vs Dubbo 3.0.15 (hoson-pod使用):
      ❌ 不支持大文件传输
      ❌ 性能无法满足AI调用
      ❌ 云原生支持不足
      ❌ 协议兼容性有限
```

###### *******.4 AI与存储技术选型

**AI服务集成: AWS SageMaker + OpenAI API**

```yaml
DTF AI需求分析:
  - 图像处理: 需要GPU加速
  - 内容生成: 需要大模型支持
  - 实时推理: 需要低延迟响应
  - 成本控制: 需要弹性扩缩容
  
  技术选型:
    AWS SageMaker:
      ✅ 原生GPU支持
      ✅ 自动扩缩容
      ✅ 模型版本管理
      ✅ 成本优化
  
    OpenAI API:
      ✅ 最先进的AI能力
      ✅ 快速集成
      ✅ 持续更新
      ✅ 全球可用
```

**文件存储: AWS S3 + CloudFront CDN**

```yaml
DTF存储需求:
  - 图像文件: 全球分布式存储
  - 设计文件: 版本管理和备份
  - 用户数据: 合规存储要求
  - 访问优化: 全球加速需求
  
  AWS S3优势:
    - 全球分布: 支持多地区部署
    - 版本控制: 支持文件版本管理
    - 生命周期: 自动成本优化
    - 安全合规: 满足各国法规
  
  vs 腾讯云COS (hoson-pod使用):
    ✅ 全球覆盖更好
    ✅ 合规认证更全
    ✅ 生态集成更强
    ✅ 成本控制更灵活
```

###### *******.5 监控与安全技术选型

**链路追踪: SkyWalking 9.0.0**

```yaml
DTF复杂调用链需求:
  - AI服务调用链追踪
  - 跨地区服务调用
  - 性能瓶颈分析
  - 错误根因分析
  
  SkyWalking 9.0.0优势:
    - 云原生支持
    - AI服务监控
    - 全球化部署
    - 性能优化
```

**安全认证: Spring Security 6.2.4 + JWT 0.11.5**

```yaml
DTF安全需求:
  - 多租户权限隔离
  - 全球合规要求
  - API安全防护
  - 用户隐私保护
  
  技术选型理由:
    Spring Security 6.2.4:
      ✅ 支持OAuth2/OIDC
      ✅ 多租户权限控制
      ✅ 合规审计支持
  
    JWT 0.11.5:
      ✅ 稳定性验证
      ✅ 安全漏洞修复
      ✅ 性能优化
```

###### *******.6 技术选型总结

**整体策略: **

1. **业务驱动**: 基于DTF平台的实际业务需求选择技术
2. **前瞻性**: 选择能支撑未来3-5年发展的技术版本
3. **稳定性**: 优先选择经过验证的稳定版本
4. **生态兼容**: 确保技术栈之间的良好兼容性
5. **团队能力**: 考虑团队的技术储备和学习成本

**与hoson-pod的差异化: **

- hoson-pod: 稳定优先，适合B端API服务
- DTF平台: 性能优先，适合C端高并发场景
- 两个平台可以并存，逐步技术栈统一

#### 7.1.3 模块职责划分

```yaml
dtf-tool-parent:
  职责: Maven父模块，统一依赖版本管理
  功能:
    - 定义所有子模块的依赖版本
    - 统一构建配置和插件版本
    - 代码质量检查配置
    - 部署和发布配置
  
  关键配置:
    - Java 17编译配置
    - Spring Boot版本锁定
    - 第三方库版本管理
    - Maven插件版本控制

dtf-tool-common:
  职责: 公共基础模块，提供通用功能
  功能:
    - 通用工具类和常量定义
    - 统一异常处理框架
    - 响应结果封装
    - 自定义验证注解
    - 公共配置类
  
  核心组件:
    - ResponseResult: 统一响应格式
    - GlobalExceptionHandler: 全局异常处理
    - ValidationGroups: 验证分组
    - CommonConstants: 公共常量
    - DateUtils/StringUtils: 工具类

dtf-tool-gateway:
  职责: API网关，统一入口和路由管理
  功能:
    - 统一API入口和路由转发
    - 认证授权和权限控制
    - 限流熔断和负载均衡
    - 跨域处理和安全防护
    - 监控指标收集
  
  核心组件:
    - GatewayConfig: 网关配置
    - AuthFilter: 认证过滤器
    - RateLimitFilter: 限流过滤器
    - CorsConfig: 跨域配置
    - RouteConfig: 路由配置

dtf-tool-tenant:
  职责: 租户管理微服务，核心业务逻辑
  功能:
    - 多租户管理和数据隔离
    - 用户认证和权限管理
    - Google OAuth2集成
    - JWT+Redis会话管理
    - 异步审计日志
  
  架构模式:
    - DDD领域驱动设计
    - 六边形架构(端口适配器)
    - CQRS命令查询分离
    - 事件驱动架构
    - 微服务架构模式
```

### 7.2 领域设计

#### 7.2.1 微服务

##### 7.2.1.1 账户域微服务

```yaml
账户域微服务:
  - 用户服务 (User Service)
  - 认证服务 (Auth Service)
  - 租户服务 (Tenant Service)

用户服务 (User Service):
  职责: 用户基础信息管理、用户生命周期、Google邮箱认证
  技术栈: Spring Cloud + Spring Boot + Spring Security + Google OAuth2 + MySQL + Redis
  API设计:
    # 用户注册登录
    - POST /api/users/register - 用户注册
    - POST /api/users/google-login - Google邮箱登录
    - POST /api/users/google-callback - Google OAuth回调
    - POST /api/users/login - 传统邮箱登录
    - POST /api/users/logout - 用户登出
  
    # 用户信息管理
    - GET /api/users/profile - 获取用户信息
    - PUT /api/users/profile - 更新用户信息
    - DELETE /api/users/{id} - 删除用户(软删除)
    - GET /api/users/{id}/sessions - 获取用户会话列表
    - DELETE /api/users/sessions/{sessionId} - 注销指定会话

认证服务 (Auth Service):
  职责: 身份认证、JWT令牌管理、权限验证、多因子认证
  技术栈: Spring Security + OAuth2 + JWT + Google OAuth2
  API设计:
    # 认证相关
    - POST /api/auth/login - 登录认证
    - POST /api/auth/google-auth - Google认证
    - POST /api/auth/refresh - 刷新令牌
    - POST /api/auth/logout - 登出
    - GET /api/auth/verify - 令牌验证
  
    # 多因子认证
    - POST /api/auth/mfa/enable - 启用MFA（可选）
    - POST /api/auth/mfa/verify - 验证MFA（可选）
    - POST /api/auth/mfa/disable - 禁用MFA（可选）

租户服务 (Tenant Service):
  职责: 多租户管理、租户配置、数据隔离、主子租户体系
  技术栈: Spring Cloud + Spring Boot + MySQL + Redis + Dubbo
  API设计:
    # 租户基础管理
    - POST /api/tenants/register - 租户注册
    - GET /api/tenants/{id} - 根据ID查询租户
    - GET /api/tenants/code/{tenantCode} - 根据租户代码查询租户
    - PUT /api/tenants/{id} - 更新租户信息
    - DELETE /api/tenants/{id} - 删除租户(软删除)
  
    # 租户状态管理
    - PUT /api/tenants/{id}/activate - 激活租户
    - PUT /api/tenants/{id}/suspend - 停用租户
    - PUT /api/tenants/{id}/status - 更新租户状态
  
    # 租户查询
    - GET /api/tenants - 分页查询租户列表
    - GET /api/tenants/search - 条件搜索租户
    - GET /api/tenants/{id}/statistics - 获取租户统计信息
  
    # 主子租户管理
    - GET /api/tenants/{parentId}/sub-tenants - 查询子租户列表
    - POST /api/tenants/{parentId}/sub-tenants - 创建子租户
    - PUT /api/tenants/{parentId}/sub-tenants/{subId} - 更新子租户
    - DELETE /api/tenants/{parentId}/sub-tenants/{subId} - 删除子租户
  
    # 租户配置管理
    - GET /api/tenants/{id}/config - 获取租户配置
    - PUT /api/tenants/{id}/config - 更新租户配置
    - GET /api/tenants/{id}/users - 获取租户用户列表
    - POST /api/tenants/{id}/users - 添加租户用户
    - DELETE /api/tenants/{id}/users/{userId} - 移除租户用户
```

##### 7.2.1.1.2 设备域微服务

```yaml

设备域微服务:
  - 设备服务 (Device Service)
  - 监控服务 (Monitor Service)

设备服务 (Device Service):
  职责: 设备注册、设备信息管理、设备状态、租户设备隔离
  技术栈: Spring Cloud + Spring Boot + MySQL + MQTT + Redis
  API设计:
    # 设备管理
    - POST /api/devices/register - 设备注册
    - GET /api/devices/{id} - 获取设备信息
    - PUT /api/devices/{id} - 更新设备信息
    - PUT /api/devices/{id}/status - 更新设备状态
    - DELETE /api/devices/{id} - 删除设备
  
    # 租户设备管理
    - GET /api/devices/tenant/{tenantId} - 获取租户设备列表
    - GET /api/devices/tenant/{tenantId}/statistics - 租户设备统计
    - POST /api/devices/tenant/{tenantId}/batch-import - 批量导入设备
  
    # 设备监控
    - GET /api/devices/{id}/status - 获取设备状态
    - GET /api/devices/{id}/logs - 获取设备日志
    - POST /api/devices/{id}/commands - 发送设备命令

监控服务 (Monitor Service):
  职责: 设备监控、性能分析、告警管理
  技术栈: Spring Cloud + Spring Boot + InfluxDB + Grafana + Kafka
  API设计:
    - GET /api/monitor/devices/{id}/metrics - 设备性能指标
    - GET /api/monitor/tenant/{tenantId}/dashboard - 租户监控面板
    - POST /api/monitor/alerts/rules - 创建告警规则
    - GET /api/monitor/alerts/history - 告警历史记录
```

##### 7.2.1.1.3 内容域微服务

```yaml

内容域微服务:
  - 图库服务 (Gallery Service)
  - 社区服务 (Community Service)
  - 内容服务 (Content Service)

图库服务 (Gallery Service):
  职责: 图库管理、内容分发、版权保护、租户内容隔离
  技术栈: Spring Cloud + Spring Boot + MySQL + AWS S3 + CDN
  API设计:
    - POST /api/gallery/upload - 上传图片
    - GET /api/gallery/tenant/{tenantId}/items - 租户图库列表
    - GET /api/gallery/items/{id} - 获取图片详情
    - PUT /api/gallery/items/{id} - 更新图片信息
    - DELETE /api/gallery/items/{id} - 删除图片

社区服务 (Community Service):
  职责: 社区内容、用户互动、内容审核
  技术栈: Spring Cloud + Spring Boot + MySQL + Elasticsearch + Redis
  API设计:
    - POST /api/community/posts - 发布帖子
    - GET /api/community/tenant/{tenantId}/posts - 租户社区内容
    - POST /api/community/posts/{id}/comments - 评论帖子
    - PUT /api/community/posts/{id}/like - 点赞帖子

内容服务 (Content Service):
  职责: 内容管理、格式转换、CDN分发
  技术栈: Spring Cloud + Spring Boot + AWS S3 + Lambda + CloudFront
  API设计:
    - POST /api/content/upload - 内容上传
    - GET /api/content/{id}/download - 内容下载
    - POST /api/content/{id}/convert - 格式转换
    - GET /api/content/tenant/{tenantId}/statistics - 内容统计
```

##### 7.2.1.1.4 商务域微服务

```yaml

商务域微服务:
 - 商城服务 (Mall Service)
 - 订单服务 (Order Service)
 - 支付服务 (Payment Service)
 - 钱包服务 (Wallet Service)

商城服务 (Mall Service):
  职责: 商品管理、库存管理、租户商城定制
  技术栈: Spring Cloud + Spring Boot + MySQL + Redis + Elasticsearch
  API设计:
    - POST /api/mall/products - 创建商品
    - GET /api/mall/tenant/{tenantId}/products - 租户商品列表
    - PUT /api/mall/products/{id} - 更新商品信息
    - GET /api/mall/products/search - 商品搜索
    - PUT /api/mall/products/{id}/inventory - 更新库存

订单服务 (Order Service):
  职责: 订单处理、订单状态管理、物流跟踪、租户订单隔离
  技术栈: Spring Cloud + Spring Boot + MySQL + RabbitMQ + Redis
  API设计:
    - POST /api/orders - 创建订单
    - GET /api/orders/{id} - 获取订单详情
    - PUT /api/orders/{id}/status - 更新订单状态
    - GET /api/orders/user/{userId} - 获取用户订单
    - GET /api/orders/tenant/{tenantId} - 获取租户订单列表
    - PUT /api/orders/{id}/cancel - 取消订单
    - GET /api/orders/{id}/logistics - 物流跟踪

支付服务 (Payment Service) - 策略模式设计:
  职责: 多支付方式处理、支付回调、退款处理、策略模式实现
  技术栈: Spring Cloud + Spring Boot + Strategy Pattern + Stripe API + PayPal API
  API设计:
    # 支付策略接口
    - POST /api/payments/strategies - 获取可用支付策略
    - GET /api/payments/strategies/{type}/config - 获取支付策略配置
  
    # 支付处理 (策略模式)
    - POST /api/payments/charge - 发起支付 (自动选择策略)
    - POST /api/payments/stripe/charge - Stripe支付
    - POST /api/payments/paypal/charge - PayPal支付
    - POST /api/payments/alipay/charge - 支付宝支付
    - POST /api/payments/wechat/charge - 微信支付
  
    # 支付回调 (策略模式)
    - POST /api/payments/webhook/stripe - Stripe回调
    - POST /api/payments/webhook/paypal - PayPal回调
    - POST /api/payments/webhook/alipay - 支付宝回调
    - POST /api/payments/webhook/wechat - 微信回调
  
    # 退款处理 (策略模式)
    - POST /api/payments/refund - 申请退款 (自动选择策略)
    - POST /api/payments/stripe/refund - Stripe退款
    - POST /api/payments/paypal/refund - PayPal退款
  
    # 支付查询
    - GET /api/payments/{id}/status - 查询支付状态
    - GET /api/payments/tenant/{tenantId}/transactions - 租户交易记录

钱包服务 (Wallet Service):
  职责: 积分管理、余额管理、交易记录、多币种支持
  技术栈: Spring Cloud + Spring Boot + MySQL + Redis + 分布式锁
  API设计:
    # 钱包管理
    - GET /api/wallet/balance - 获取余额
    - GET /api/wallet/tenant/{tenantId}/balance - 租户钱包余额
    - POST /api/wallet/create - 创建钱包账户
  
    # 交易操作
    - POST /api/wallet/recharge - 充值积分
    - POST /api/wallet/consume - 消费积分
    - POST /api/wallet/transfer - 转账操作
    - POST /api/wallet/freeze - 冻结资金
    - POST /api/wallet/unfreeze - 解冻资金
  
    # 交易记录
    - GET /api/wallet/transactions - 交易记录
    - GET /api/wallet/tenant/{tenantId}/transactions - 租户交易记录
    - GET /api/wallet/transactions/{id} - 交易详情
    - GET /api/wallet/statements - 账单明细
```

##### 7.2.1.1.5 AI域微服务

```yaml

AI域微服务:
  - AI服务 (AI Service)
  - AWS SageMaker集成
  - AWS Lambda处理器

AI服务 (AI Service):
  职责: AI模型调用、图像处理、智能推荐、租户AI配额管理
  技术栈: Spring Cloud + Spring Boot + AWS SageMaker + Lambda + Python
  API设计:
    # AI处理
    - POST /api/ai/image/enhance - 图像增强
    - POST /api/ai/image/generate - AI图像生成
    - POST /api/ai/text/generate - AI文本生成
    - POST /api/ai/recommendation - 智能推荐
  
    # 租户AI管理
    - GET /api/ai/tenant/{tenantId}/quota - 租户AI配额
    - GET /api/ai/tenant/{tenantId}/usage - 租户AI使用统计
    - PUT /api/ai/tenant/{tenantId}/models - 配置租户AI模型
  
    # AI任务管理
    - GET /api/ai/tasks/{id} - 获取AI任务状态
    - GET /api/ai/tasks/tenant/{tenantId} - 租户AI任务列表
    - DELETE /api/ai/tasks/{id} - 取消AI任务

AWS SageMaker集成:
  - 模型训练和部署
  - 实时推理服务
  - 批量处理任务

AWS Lambda处理器:
  - 轻量级AI任务
  - 图像预处理
  - 结果后处理
```

##### 7.2.1.1.6 存储域微服务

```yaml

存储域微服务:
 - 存储服务 (Storage Service)
 - AWS S3存储
 - AWS CloudFront CDN


存储服务 (Storage Service):
  职责: 文件存储、CDN分发、存储配额管理、租户存储隔离
  技术栈: Spring Cloud + Spring Boot + AWS S3 + CloudFront + MySQL
  API设计:
    # 文件操作
    - POST /api/storage/upload - 文件上传
    - GET /api/storage/download/{fileId} - 文件下载
    - DELETE /api/storage/files/{fileId} - 删除文件
    - GET /api/storage/files/{fileId}/info - 文件信息
  
    # 租户存储管理
    - GET /api/storage/tenant/{tenantId}/quota - 租户存储配额
    - GET /api/storage/tenant/{tenantId}/usage - 租户存储使用量
    - GET /api/storage/tenant/{tenantId}/files - 租户文件列表
    - PUT /api/storage/tenant/{tenantId}/quota - 更新存储配额
  
    # CDN管理
    - POST /api/storage/cdn/invalidate - CDN缓存刷新
    - GET /api/storage/cdn/statistics - CDN统计数据

AWS S3存储:
  - 多区域存储
  - 生命周期管理
  - 版本控制
  - 跨区域复制
```

### 7.2 微服务详细设计

#### 7.2.1 微服务API设计 - 基于设计模式的灵活架构

##### 7.2.1.1 账户域微服务API设计

**用户服务 (User Service) - 采用CQRS模式**

```yaml
命令端API (写操作):
  用户注册管理:
    - POST /api/users/commands/register
      含义: 用户邮箱注册，支持Google邮箱快速注册
      业务价值: 降低注册门槛，提升用户转化率
  
    - POST /api/users/commands/verify-email
      含义: 邮箱验证激活，确保邮箱真实性
      业务价值: 保证用户质量，减少垃圾账户
  
    - POST /api/users/commands/resend-verification
      含义: 重新发送验证邮件，提升用户体验
      业务价值: 解决邮件丢失问题，提高激活成功率
  
    - PUT /api/users/commands/{userId}/profile
      含义: 更新用户基础信息(头像、名称、联系方式)
      业务价值: 完善用户画像，支持个性化服务
  
    - PUT /api/users/commands/{userId}/avatar
      含义: 单独更新用户头像，支持图片上传
      业务价值: 提升用户个性化体验，增强平台粘性
  
    - PUT /api/users/commands/{userId}/status
      含义: 管理员操作用户状态(激活/禁用/删除)
      业务价值: 平台治理和风险控制

  多端登录管理:
    - POST /api/users/commands/login
      含义: 用户登录认证，支持PC端和移动端
      业务价值: 统一登录体验，支持多端数据同步
  
    - POST /api/users/commands/logout
      含义: 用户主动登出，清理会话信息
      业务价值: 保护用户隐私，释放系统资源
  
    - POST /api/users/commands/logout-all-devices
      含义: 强制登出所有设备，安全防护功能
      业务价值: 账户安全保护，防止账户被盗用
  
    - PUT /api/users/commands/{userId}/password
      含义: 修改用户密码，支持忘记密码重置
      业务价值: 账户安全管理，用户自主控制

查询端API (读操作):
  用户信息查询:
    - GET /api/users/queries/{userId}/profile
      含义: 获取用户完整档案信息
      业务价值: 支持个人中心展示，用户信息管理
  
    - GET /api/users/queries/{userId}/sessions
      含义: 查看当前用户所有登录会话
      业务价值: 安全监控，用户可主动管理登录设备
  
    - GET /api/users/queries/search
      含义: 管理员搜索用户，支持多条件筛选
      业务价值: 平台运营管理，用户服务支持
  
    - GET /api/users/queries/{userId}/login-history
      含义: 查看用户登录历史记录
      业务价值: 安全审计，异常登录检测

设计模式应用:
  - CQRS模式: 读写分离，优化查询性能，支持高并发
  - 命令模式: 封装用户操作，支持撤销和重做
  - 观察者模式: 用户状态变更通知其他服务
  - 策略模式: 支持多种登录方式(邮箱、手机、第三方)
```

**认证服务 (Auth Service) - 采用策略模式**

```yaml
认证策略API:
  多端认证支持:
    - POST /api/auth/strategies/email/authenticate
      含义: 邮箱密码认证，传统登录方式
      业务价值: 兼容传统用户习惯，保证登录成功率
  
    - POST /api/auth/strategies/mobile/authenticate
      含义: 手机号验证码认证，快速登录
      业务价值: 提升移动端用户体验，降低登录门槛
  
    - POST /api/auth/strategies/oauth/{provider}/authenticate
      含义: 第三方OAuth认证(Google/Apple/Facebook)
      业务价值: 一键登录，提升用户转化率
  
    - POST /api/auth/strategies/device/authenticate
      含义: 设备指纹认证，免密登录
      业务价值: 提升老用户体验，减少重复认证

  令牌管理:
    - POST /api/auth/tokens/generate
      含义: 生成JWT访问令牌和刷新令牌
      业务价值: 无状态认证，支持分布式部署
  
    - POST /api/auth/tokens/refresh
      含义: 刷新过期令牌，保持登录状态
      业务价值: 用户无感知续期，提升使用体验
  
    - POST /api/auth/tokens/validate
      含义: 验证令牌有效性，权限检查
      业务价值: 接口安全保护，防止非法访问
  
    - DELETE /api/auth/tokens/revoke
      含义: 撤销令牌，强制下线
      业务价值: 安全防护，账户异常时快速响应

  会话管理:
    - GET /api/auth/sessions/current
      含义: 获取当前会话详细信息
      业务价值: 用户了解登录状态，安全透明化
  
    - GET /api/auth/sessions/all
      含义: 获取用户所有活跃会话列表
      业务价值: 多端登录管理，安全监控
  
    - DELETE /api/auth/sessions/{sessionId}
      含义: 删除指定会话，远程登出
      业务价值: 精确控制登录设备，安全管理
  
    - PUT /api/auth/sessions/{sessionId}/extend
      含义: 延长会话有效期，保持活跃
      业务价值: 长时间使用场景，避免频繁重登

设计模式应用:
  - 策略模式: 支持多种认证策略，易于扩展新的认证方式
  - 工厂模式: 根据认证类型创建对应的认证器
  - 装饰器模式: 为认证过程添加额外功能(日志、限流等)
  - 责任链模式: 多层认证验证流程，安全性更高
```

**租户服务 (Tenant Service) - 采用多租户模式**

```yaml
租户管理API:
  租户生命周期:
    - POST /api/tenants/commands/create
      含义: 创建新租户，支持设备厂商入驻
      业务价值: 平台扩展能力，支持多厂商生态
  
    - PUT /api/tenants/commands/{tenantId}/configure
      含义: 配置租户个性化设置(品牌、功能模块)
      业务价值: OEM定制化服务，满足厂商差异化需求
  
    - PUT /api/tenants/commands/{tenantId}/status
      含义: 管理租户状态(激活/暂停/禁用)
      业务价值: 平台治理，合规风控管理
  
    - DELETE /api/tenants/commands/{tenantId}/archive
      含义: 归档租户数据，软删除处理
      业务价值: 数据保护，支持恢复和审计

  租户查询:
    - GET /api/tenants/queries/{tenantId}/info
      含义: 获取租户基础信息和配置
      业务价值: 租户自服务，信息透明化管理
  
    - GET /api/tenants/queries/{tenantId}/users
      含义: 查询租户下所有用户列表
      业务价值: 租户用户管理，权限分配基础
  
    - GET /api/tenants/queries/{tenantId}/statistics
      含义: 租户运营数据统计分析
      业务价值: 数据驱动决策，业务增长洞察
  
    - GET /api/tenants/queries/search
      含义: 平台管理员搜索租户
      业务价值: 平台运营管理，客户服务支持

设计模式应用:
  - 多租户模式: 数据隔离和资源共享，降低运营成本
  - 模板方法模式: 标准化租户创建流程，保证一致性
  - 建造者模式: 灵活构建租户配置，支持个性化定制
  - 适配器模式: 适配不同租户的个性化需求
```

##### 7.2.1.2 钱包域微服务API设计

**钱包服务 (Wallet Service) - 采用账户模式**

```yaml
账户管理API:
  钱包生命周期:
    - POST /api/wallets/commands/create
      含义: 用户注册时自动创建钱包账户
      业务价值: 用户即开即用，降低使用门槛
  
    - GET /api/wallets/queries/{userId}/balance
      含义: 查询用户积分余额，实时显示
      业务价值: 透明化资产管理，提升用户信任
  
    - GET /api/wallets/queries/{userId}/accounts
      含义: 查询用户所有账户信息(积分/现金)
      业务价值: 多账户管理，支持复杂业务场景
  
    - PUT /api/wallets/commands/{walletId}/freeze
      含义: 冻结钱包，风控安全措施
      业务价值: 风险防控，保护用户资产安全
  
    - PUT /api/wallets/commands/{walletId}/unfreeze
      含义: 解冻钱包，恢复正常使用
      业务价值: 灵活风控管理，用户体验平衡

  积分管理:
    - POST /api/wallets/commands/{walletId}/points/add
      含义: 增加用户积分(充值/赠送/奖励)
      业务价值: 用户激励体系，促进平台活跃度
  
    - POST /api/wallets/commands/{walletId}/points/deduct
      含义: 扣减用户积分(消费/退款)
      业务价值: 支持积分消费，完整交易闭环
  
    - POST /api/wallets/commands/{walletId}/points/transfer
      含义: 积分转账功能(用户间转移)
      业务价值: 社交化功能，增强用户粘性
  
    - GET /api/wallets/queries/{walletId}/points/balance
      含义: 实时查询积分余额
      业务价值: 用户资产透明，消费决策支持
  
    - GET /api/wallets/queries/{walletId}/points/transactions
      含义: 积分流水查询，支持分页和筛选
      业务价值: 账单明细，用户消费行为分析

设计模式应用:
  - 账户模式: 复式记账，确保资金安全和数据一致性
  - 状态模式: 钱包状态管理(正常、冻结、注销)
  - 备忘录模式: 支持交易回滚和状态恢复
  - 原子操作模式: 确保积分操作的原子性，防止数据不一致
```

**订单服务 (Order Service) - 采用状态机模式**

```yaml
订单生命周期API:
  订单管理:
    - POST /api/orders/commands/create
      含义: 创建订单(积分充值/商品购买)
      业务价值: 交易流程起点，支持多种商品类型
  
    - PUT /api/orders/commands/{orderId}/confirm
      含义: 确认订单，进入支付流程
      业务价值: 用户确认机制，减少误操作
  
    - PUT /api/orders/commands/{orderId}/cancel
      含义: 取消订单，释放库存和积分
      业务价值: 用户自主控制，提升购买体验
  
    - PUT /api/orders/commands/{orderId}/complete
      含义: 完成订单，交易成功处理
      业务价值: 交易闭环，触发后续业务流程
  
    - GET /api/orders/queries/{orderId}/status
      含义: 实时查询订单状态
      业务价值: 订单跟踪，用户体验透明化

  订单查询:
    - GET /api/orders/queries/{userId}/list
      含义: 用户订单列表，支持状态筛选
      业务价值: 个人订单管理，消费历史回顾
  
    - GET /api/orders/queries/{orderId}/details
      含义: 订单详情查询，完整信息展示
      业务价值: 订单详情确认，客服支持基础
  
    - GET /api/orders/queries/{userId}/statistics
      含义: 用户消费统计分析
      业务价值: 用户画像构建，个性化推荐基础
  
    - GET /api/orders/queries/search
      含义: 管理员订单搜索，运营分析
      业务价值: 平台运营数据，业务决策支持

设计模式应用:
  - 状态机模式: 订单状态流转管理，业务流程清晰
  - 命令模式: 订单操作的封装和执行，支持撤销
  - 观察者模式: 订单状态变更通知相关服务
  - 补偿模式: 订单失败时的补偿机制，保证数据一致性
```

**支付服务 (Payment Service) - 采用适配器模式**

```yaml
支付网关API:
  支付方式适配:
    - POST /api/payments/gateways/credit-card/charge
      含义: 信用卡支付处理，主要支付方式
      业务价值: 覆盖主流用户群体，提高支付成功率
  
    - POST /api/payments/gateways/paypal/charge
      含义: PayPal支付处理，海外用户首选
      业务价值: 全球化支付支持，扩大用户覆盖
  
    - POST /api/payments/gateways/apple-pay/charge
      含义: Apple Pay支付，iOS用户便捷支付
      业务价值: 移动端优化体验，提升转化率
  
    - POST /api/payments/gateways/google-pay/charge
      含义: Google Pay支付，Android用户支持
      业务价值: 多平台支付覆盖，用户选择灵活

  支付管理:
    - POST /api/payments/commands/initiate
      含义: 发起支付请求，创建支付会话
      业务价值: 支付流程启动，安全性和用户体验平衡
  
    - PUT /api/payments/commands/{paymentId}/confirm
      含义: 确认支付完成，更新订单状态
      业务价值: 支付确认机制，防止重复扣款
  
    - PUT /api/payments/commands/{paymentId}/refund
      含义: 处理退款请求，资金原路返回
      业务价值: 售后服务支持，用户权益保护
  
    - GET /api/payments/queries/{paymentId}/status
      含义: 查询支付状态，实时跟踪
      业务价值: 支付透明化，异常处理支持

  支付查询:
    - GET /api/payments/queries/{userId}/history
      含义: 用户支付历史记录，分页展示
      业务价值: 个人财务管理，消费行为分析
  
    - GET /api/payments/queries/{paymentId}/details
      含义: 支付详情查询，完整交易信息
      业务价值: 交易凭证，客服支持和争议处理
  
    - GET /api/payments/queries/{userId}/methods
      含义: 用户绑定的支付方式管理
      业务价值: 支付方式管理，快捷支付支持
  
    - GET /api/payments/queries/statistics
      含义: 平台支付数据统计分析
      业务价值: 业务数据洞察，运营决策支持

设计模式应用:
  - 适配器模式: 统一不同支付网关接口，降低集成复杂度
  - 策略模式: 支持多种支付策略，灵活选择最优方案
  - 工厂模式: 根据支付方式创建处理器，代码解耦
  - 重试模式: 支付失败时的重试机制，提高成功率
```

##### 7.2.1.3 存储域微服务API设计

**存储服务 (Storage Service) - 采用代理模式**

```yaml
文件管理API:
  文件操作:
    - POST /api/storage/files/upload/initiate
      含义: 初始化文件上传，获取上传凭证
      业务价值: 安全上传机制，支持大文件分片上传
  
    - PUT /api/storage/files/{fileId}/upload/chunk
      含义: 分片上传文件块，支持断点续传
      业务价值: 大文件上传优化，网络异常容错
  
    - POST /api/storage/files/{fileId}/upload/complete
      含义: 完成文件上传，合并分片
      业务价值: 文件完整性校验，上传流程完结
  
    - GET /api/storage/files/{fileId}/download
      含义: 文件下载，支持CDN加速
      业务价值: 全球化访问优化，用户体验提升
  
    - DELETE /api/storage/files/{fileId}
      含义: 删除文件，释放存储空间
      业务价值: 存储成本控制，用户数据管理

  文件查询:
    - GET /api/storage/files/{userId}/list
      含义: 用户文件列表，支持文件夹结构
      业务价值: 个人云盘功能，文件组织管理
  
    - GET /api/storage/files/{fileId}/info
      含义: 文件详细信息查询
      业务价值: 文件属性展示，版本管理支持
  
    - GET /api/storage/files/{fileId}/versions
      含义: 文件版本历史查询
      业务价值: 版本控制，误删恢复支持
  
    - GET /api/storage/files/search
      含义: 文件搜索功能，支持多条件筛选
      业务价值: 快速文件定位，提升使用效率

  配额管理:
    - GET /api/storage/quotas/{userId}/current
      含义: 查询用户当前存储配额使用情况
      业务价值: 存储透明化，用户自主管理
  
    - PUT /api/storage/quotas/{userId}/upgrade
      含义: 升级存储配额，付费扩容
      业务价值: 增值服务，平台收入来源
  
    - GET /api/storage/quotas/{userId}/usage
      含义: 存储使用量统计分析
      业务价值: 用户行为分析，产品优化依据
  
    - GET /api/storage/quotas/{userId}/history
      含义: 存储使用历史记录
      业务价值: 使用趋势分析，容量规划支持

设计模式应用:
  - 代理模式: 代理AWS S3操作，添加业务逻辑和权限控制
  - 装饰器模式: 为文件操作添加权限、日志、缓存等功能
  - 组合模式: 文件夹和文件的统一管理，树形结构支持
  - 缓存模式: 文件元数据缓存优化，提升访问性能
```

##### 7.2.1.4 首页聚合API设计

**首页服务 (Homepage Service) - 采用门面模式**

```yaml
聚合查询API:
  首页数据聚合:
    - GET /api/homepage/dashboard/{userId}
      含义: 用户个性化首页数据聚合
      业务价值: 一站式信息展示，提升用户体验
  
    - GET /api/homepage/recent-files/{userId}
      含义: 最近打开文件列表，快速访问
      业务价值: 工作效率提升，用户习惯适配
  
    - GET /api/homepage/recommendations/{userId}
      含义: 个性化推荐内容(图库/商品/教程)
      业务价值: 内容发现，促进用户活跃和消费
  
    - GET /api/homepage/quick-actions/{userId}
      含义: 快捷操作入口，常用功能聚合
      业务价值: 操作便捷化，提升产品易用性

  个性化推荐:
    - GET /api/homepage/gallery/recommended
      含义: 推荐图库内容，基于用户偏好
      业务价值: 内容消费促进，图库变现支持
  
    - GET /api/homepage/products/recommended
      含义: 推荐商品，个性化商城展示
      业务价值: 商品销售促进，电商转化提升
  
    - GET /api/homepage/templates/recommended
      含义: 推荐设计模板，创作灵感支持
      业务价值: 创作效率提升，用户价值增强
  
    - GET /api/homepage/tutorials/recommended
      含义: 推荐教程内容，用户成长支持
      业务价值: 用户教育，产品粘性增强

设计模式应用:
  - 门面模式: 简化客户端调用，聚合多个服务的复杂交互
  - 组合模式: 组合多个数据源的结果，统一返回格式
  - 缓存模式: 首页数据缓存优化，提升加载速度
  - 异步模式: 非关键数据异步加载，优化首屏体验
```

##### 7.2.1.5 跨服务通信API设计

**事件总线API - 采用发布订阅模式**

```yaml
事件发布订阅:
  事件发布:
    - POST /api/events/publish
      含义: 发布业务事件，触发下游处理
      业务价值: 服务解耦，异步处理提升性能
  
    - POST /api/events/batch-publish
      含义: 批量发布事件，提升处理效率
      业务价值: 批处理优化，减少网络开销
  
    - GET /api/events/{eventId}/status
      含义: 查询事件处理状态
      业务价值: 事件跟踪，异常处理支持

  事件订阅:
    - POST /api/events/subscriptions/create
      含义: 创建事件订阅，注册监听器
      业务价值: 灵活的事件处理机制
  
    - PUT /api/events/subscriptions/{subscriptionId}/update
      含义: 更新订阅配置，动态调整
      业务价值: 运行时配置调整，系统灵活性
  
    - DELETE /api/events/subscriptions/{subscriptionId}
      含义: 删除事件订阅，停止监听
      业务价值: 资源释放，系统优化
  
    - GET /api/events/subscriptions/{serviceId}/list
      含义: 查询服务的所有订阅
      业务价值: 订阅管理，依赖关系梳理

设计模式应用:
  - 发布订阅模式: 服务间松耦合通信，提升系统可扩展性
  - 观察者模式: 事件监听和处理，响应式架构支持
  - 消息队列模式: 异步事件处理，系统性能优化
  - 重试模式: 事件处理失败重试，保证数据一致性
```

##### ******* API网关设计

**网关服务 (Gateway Service) - 采用网关模式**

```yaml
路由管理:
  动态路由:
    - GET /api/gateway/routes/discovery
      含义: 服务发现，自动路由注册
      业务价值: 微服务自动化管理，运维效率提升
  
    - POST /api/gateway/routes/register
      含义: 手动注册路由规则
      业务价值: 灵活路由配置，特殊场景支持
  
    - PUT /api/gateway/routes/{routeId}/update
      含义: 更新路由配置，动态调整
      业务价值: 运行时配置变更，零停机部署
  
    - DELETE /api/gateway/routes/{routeId}
      含义: 删除路由规则，服务下线
      业务价值: 服务生命周期管理

  负载均衡:
    - GET /api/gateway/load-balancer/strategies
      含义: 查询负载均衡策略
      业务价值: 性能优化配置，系统稳定性保障
  
    - PUT /api/gateway/load-balancer/configure
      含义: 配置负载均衡算法
      业务价值: 流量分配优化，服务性能提升
  
    - GET /api/gateway/load-balancer/health
      含义: 健康检查状态查询
      业务价值: 服务可用性监控，故障快速发现

  限流熔断:
    - POST /api/gateway/rate-limiter/configure
      含义: 配置API限流规则
      业务价值: 系统保护，防止过载和攻击
  
    - GET /api/gateway/rate-limiter/status
      含义: 查询限流状态统计
      业务价值: 流量监控，容量规划支持
  
    - POST /api/gateway/circuit-breaker/configure
      含义: 配置熔断器规则
      业务价值: 故障隔离，系统稳定性保障
  
    - GET /api/gateway/circuit-breaker/status
      含义: 查询熔断器状态
      业务价值: 故障监控，快速恢复支持

设计模式应用:
  - 网关模式: 统一入口和横切关注点，简化客户端复杂度
  - 代理模式: 请求转发和响应处理，透明化服务调用
  - 装饰器模式: 添加认证、限流、日志等功能，功能增强
  - 责任链模式: 请求处理链路，灵活的处理流程
```

#### 7.2.2 API设计原则总结

**SOLID原则在API设计中的应用**:

```yaml
单一职责原则 (SRP):
  - 每个API只负责一个业务功能
  - 避免接口功能过于复杂
  - 保持接口语义清晰

开闭原则 (OCP):
  - 通过版本控制支持扩展
  - 新功能通过新接口添加
  - 避免修改现有接口

里氏替换原则 (LSP):
  - 接口实现可以相互替换
  - 保持接口契约一致性
  - 支持多种实现策略

接口隔离原则 (ISP):
  - 细粒度接口设计
  - 避免客户端依赖不需要的接口
  - 支持按需调用

依赖倒置原则 (DIP):
  - 依赖抽象而非具体实现
  - 通过接口定义服务契约
  - 支持依赖注入和测试
```

**RESTful设计最佳实践**:

```yaml
资源导向设计:
  - 使用名词而非动词
  - 层次化资源结构
  - 统一的命名约定

HTTP方法语义:
  - GET: 查询操作，幂等
  - POST: 创建操作，非幂等
  - PUT: 更新操作，幂等
  - DELETE: 删除操作，幂等

状态码规范:
  - 2xx: 成功响应
  - 4xx: 客户端错误
  - 5xx: 服务端错误
  - 自定义业务错误码
```

#### 7.2.2 领域设计

基于DDD领域驱动设计方法论，从战略设计到战术设计的完整建模过程:

##### ******* 战略设计 - 领域划分

###### *******.1 核心域 (Core Domain)

DTF玩图平台的核心竞争力和业务价值所在:

```yaml
多租户账户管理域 (Multi-Tenant Account Domain):
  业务价值: 平台的核心基础，支撑所有业务场景
  核心能力:
    - 全球化身份统一管理
    - 多租户数据完全隔离
    - Google OAuth2无缝集成
    - 企业级权限控制体系
  技术复杂度: 极高
  业务重要性: 极高
  竞争优势: 同一邮箱多厂商注册 + 全球身份统一

设备管理域 (Device Management Domain):
  业务价值: 设备厂商的核心诉求，直接影响商业价值
  核心能力:
    - 设备全生命周期管理
    - 实时监控和数据分析
    - 设备状态智能预警
    - 多厂商设备统一管理
  技术复杂度: 高
  业务重要性: 极高
  竞争优势: 跨厂商设备统一管理平台

内容创作域 (Content Creation Domain):
  业务价值: C端用户的核心体验，平台差异化竞争力
  核心能力:
    - AI辅助设计创作
    - 多端协同创作体验
    - 智能内容推荐
    - 版权保护和商业化
  技术复杂度: 高
  业务重要性: 高
  竞争优势: AI+多端+社区的创作生态
```

###### *******.2 通用域 (Generic Domain)

行业通用的标准化功能，可采用成熟方案:

```yaml
支付交易域 (Payment Transaction Domain):
  业务特点: 标准化的支付流程
  实现策略: 集成第三方支付服务(Stripe/PayPal)
  核心功能:
    - 多币种支付支持
    - 订单交易管理
    - 退款和对账
    - 合规性保障

消息通知域 (Notification Domain):
  业务特点: 标准化的消息推送
  实现策略: 集成AWS SNS/SES等服务
  核心功能:
    - 邮件通知
    - 短信通知
    - 应用内推送
    - 消息模板管理

文件存储域 (File Storage Domain):
  业务特点: 标准化的文件管理
  实现策略: 基于AWS S3的对象存储
  核心功能:
    - 文件上传下载
    - CDN加速分发
    - 文件格式转换
    - 存储配额管理
```

###### *******.3  支撑域 (Supporting Domain)

支撑核心业务运行的基础功能:

```yaml
合规审计域 (Compliance Audit Domain):
  业务价值: 支撑平台合规运营
  核心功能:
    - GDPR/CCPA数据保护
    - SOX审计日志
    - 数据保留策略
    - 隐私权利管理
  技术特点: 高度标准化，重在执行

系统监控域 (System Monitoring Domain):
  业务价值: 保障系统稳定运行
  核心功能:
    - 系统性能监控
    - 业务指标统计
    - 异常告警处理
    - 链路追踪分析
  技术特点: 基于成熟监控方案

数据分析域 (Data Analytics Domain):
  业务价值: 支撑业务决策
  核心功能:
    - 用户行为分析
    - 设备使用统计
    - 内容热度分析
    - 商业化数据洞察
  技术特点: 基于大数据分析平台
```

###### *******.4 限界上下文映射

```plantuml
@startuml title DTF Bounded Context Map

!define CORE_DOMAIN_COLOR #FFE6E6
!define GENERIC_DOMAIN_COLOR #E6F3FF
!define SUPPORTING_DOMAIN_COLOR #F0F8E6

package "核心域" CORE_DOMAIN_COLOR {
  [多租户账户管理] as AccountContext
  [设备管理] as DeviceContext
  [内容创作] as ContentContext
}

package "通用域" GENERIC_DOMAIN_COLOR {
  [支付交易] as PaymentContext
  [消息通知] as NotificationContext
  [文件存储] as StorageContext
}

package "支撑域" SUPPORTING_DOMAIN_COLOR {
  [合规审计] as ComplianceContext
  [系统监控] as MonitoringContext
  [数据分析] as AnalyticsContext
}

AccountContext -- DeviceContext : 共享内核-用户身份
AccountContext -- ContentContext : 共享内核-用户身份
DeviceContext --> ContentContext : 上游下游-设备创作内容

AccountContext --> PaymentContext : 上游下游-用户支付
ContentContext --> PaymentContext : 上游下游-内容付费
PaymentContext --> NotificationContext : 上游下游-支付通知

AccountContext --> ComplianceContext : 上游下游-合规审计
DeviceContext --> MonitoringContext : 上游下游-设备监控
ContentContext --> AnalyticsContext : 上游下游-内容分析

AccountContext --> StorageContext : 上游下游-用户存储
ContentContext --> StorageContext : 上游下游-内容存储

note right of AccountContext
共享内核关系:
- 用户身份在设备和内容域共享
- 租户隔离策略统一应用
end note

note bottom of PaymentContext
防腐层模式:
- 集成第三方支付服务SDK
- 策略模式支持多种在线支付方式、跨币种结算、下单时间节点汇率计算
end note

@enduml
```

##### ******* 战术设计 - 领域建模

###### *******.1 多租户账户管理域详细建模

**聚合设计原则**

- 聚合内强一致性，聚合间最终一致性
- 聚合根是唯一的修改入口
- 聚合边界基于业务不变性规则

```plantuml
@startuml DTF_Account_Domain_Tactical_Design

title 多租户账户管理域 - 战术设计模型

!define AGGREGATE_ROOT_COLOR #FFE6CC
!define ENTITY_COLOR #E8F5E8
!define VALUE_OBJECT_COLOR #FFF2CC
!define DOMAIN_SERVICE_COLOR #E6E6FA
!define DOMAIN_EVENT_COLOR #FFE4E1

package "用户聚合 (User Aggregate)" {
  class User <<Aggregate Root>> AGGREGATE_ROOT_COLOR {
    +userId: UserId
    +globalUserId: GlobalUserId
    +email: EmailAddress
    +userType: UserType
    +status: UserStatus
    +tenantId: TenantId
    +createdAt: DateTime
    +lastLoginAt: DateTime
    --
    +register(email, password, tenant)
    +login(credentials)
    +updateProfile(profile)
    +changePassword(oldPwd, newPwd)
    +activate()
    +suspend(reason)
    +delete()
    --
    领域不变性规则:
    - 同一租户内邮箱唯一
    - 用户状态变更需记录审计
    - 密码修改需验证旧密码
  }
  
  class UserProfile <<Entity>> ENTITY_COLOR {
    +profileId: ProfileId
    +userId: UserId
    +realName: EncryptedString
    +nickname: String
    +avatar: ImageUrl
    +phone: HashedString
    +address: EncryptedAddress
    +preferences: UserPreferences
    +privacySettings: PrivacySettings
    --
    +updatePersonalInfo(info)
    +updatePreferences(prefs)
    +updatePrivacySettings(settings)
    +exportData(): PersonalDataExport
  }
  
  class AuthenticationInfo <<Entity>> ENTITY_COLOR {
    +authId: AuthId
    +userId: UserId
    +passwordHash: HashedPassword
    +salt: Salt
    +mfaEnabled: Boolean
    +mfaSecret: EncryptedString
    +failedAttempts: Integer
    +lockedUntil: DateTime
    +lastPasswordChange: DateTime
    --
    +verifyPassword(password): Boolean
    +changePassword(newPassword)
    +enableMFA(): MFASecret
    +verifyMFA(token): Boolean
    +recordFailedAttempt()
    +resetFailedAttempts()
    +lockAccount(duration)
  }
  
  ' 值对象
  class UserId <<Value Object>> VALUE_OBJECT_COLOR {
    +value: Long
    +tenantId: TenantId
    --
    +equals(other): Boolean
    +toString(): String
  }
  
  class GlobalUserId <<Value Object>> VALUE_OBJECT_COLOR {
    +value: String (UUID)
    --
    +generate(): GlobalUserId
    +fromString(uuid): GlobalUserId
  }
  
  class EmailAddress <<Value Object>> VALUE_OBJECT_COLOR {
    +value: String
    +domain: String
    --
    +validate(): Boolean
    +normalize(): EmailAddress
    +hash(): String
  }
  
  class UserType <<Value Object>> VALUE_OBJECT_COLOR {
    +CONSUMER
    +VENDOR_ADMIN
    +AGENT
    +SUPPORT
    +DEVELOPER
    --
    +hasPermission(permission): Boolean
    +getDefaultRoles(): List<Role>
  }
  
  class UserStatus <<Value Object>> VALUE_OBJECT_COLOR {
    +PENDING_ACTIVATION
    +ACTIVE
    +SUSPENDED
    +DELETED
    --
    +canTransitionTo(newStatus): Boolean
    +getAvailableTransitions(): List<UserStatus>
  }
}

package "租户聚合 (Tenant Aggregate)" {
  class Tenant <<Aggregate Root>> AGGREGATE_ROOT_COLOR {
    +tenantId: TenantId
    +tenantCode: TenantCode
    +tenantName: TenantName
    +tenantType: TenantType
    +status: TenantStatus
    +parentTenantId: TenantId
    +deploymentInstance: DeploymentInstance
    +configuration: TenantConfiguration
    +createdAt: DateTime
    --
    +createTenant(info, parent)
    +updateConfiguration(config)
    +addUser(user): UserRegistration
    +removeUser(userId)
    +createSubTenant(info): Tenant
    +activate()
    +suspend(reason)
    +upgradeSubscription(plan)
    --
    领域不变性规则:
    - 租户代码全局唯一
    - 子租户不能超过父租户权限
    - 删除租户需清理所有关联数据
  }
  
  class TenantConfiguration <<Entity>> ENTITY_COLOR {
    +configId: ConfigId
    +tenantId: TenantId
    +brandingConfig: BrandingConfig
    +featureConfig: FeatureConfig
    +securityConfig: SecurityConfig
    +resourceLimits: ResourceLimits
    +customization: CustomizationConfig
    --
    +updateBranding(branding)
    +enableFeature(feature)
    +setResourceLimit(resource, limit)
    +validateConfiguration(): ValidationResult
  }
  
  class DeploymentInstance <<Entity>> ENTITY_COLOR {
    +instanceId: InstanceId
    +region: Region
    +deploymentType: DeploymentType
    +databaseConfig: DatabaseConfig
    +storageConfig: StorageConfig
    +networkConfig: NetworkConfig
    --
    +provision()
    +scale(resources)
    +backup()
    +migrate(newRegion)
  }
  
  ' 值对象
  class TenantCode <<Value Object>> VALUE_OBJECT_COLOR {
    +value: String
    --
    +validate(): Boolean
    +normalize(): TenantCode
  }
  
  class TenantType <<Value Object>> VALUE_OBJECT_COLOR {
    +OFFICIAL
    +DEVICE_VENDOR
    +AGENT
    +ENTERPRISE_CUSTOMER
    --
    +getDefaultFeatures(): List<Feature>
    +getResourceLimits(): ResourceLimits
  }
}

package "权限聚合 (Permission Aggregate)" {
  class Role <<Aggregate Root>> AGGREGATE_ROOT_COLOR {
    +roleId: RoleId
    +roleName: RoleName
    +roleType: RoleType
    +tenantId: TenantId
    +permissions: Set<Permission>
    +status: RoleStatus
    +createdAt: DateTime
    --
    +addPermission(permission)
    +removePermission(permission)
    +assignToUser(userId): UserRoleAssignment
    +revokeFromUser(userId)
    +clone(newName): Role
    --
    领域不变性规则:
    - 角色名在租户内唯一
    - 系统角色不可删除
    - 权限变更需审计记录
  }
  
  class Permission <<Entity>> ENTITY_COLOR {
    +permissionId: PermissionId
    +resource: Resource
    +action: Action
    +scope: PermissionScope
    +conditions: List<PermissionCondition>
    --
    +checkAccess(context): AccessResult
    +addCondition(condition)
    +removeCondition(condition)
  }
  
  class UserRoleAssignment <<Entity>> ENTITY_COLOR {
    +assignmentId: AssignmentId
    +userId: UserId
    +roleId: RoleId
    +assignedBy: UserId
    +assignedAt: DateTime
    +expiresAt: DateTime
    +status: AssignmentStatus
    --
    +assign()
    +revoke()
    +extend(newExpiryDate)
    +isActive(): Boolean
    +isExpired(): Boolean
  }
  
  ' 值对象
  class Resource <<Value Object>> VALUE_OBJECT_COLOR {
    +USER, DEVICE, CONTENT, ORDER, WALLET, AI_TOOL, STORAGE
    --
    +getAvailableActions(): List<Action>
  }
  
  class Action <<Value Object>> VALUE_OBJECT_COLOR {
    +CREATE, READ, UPDATE, DELETE, EXECUTE, APPROVE, AUDIT
    --
    +requiresElevatedPrivileges(): Boolean
  }
  
  class PermissionScope <<Value Object>> VALUE_OBJECT_COLOR {
    +GLOBAL, TENANT, USER, RESOURCE_SPECIFIC
    --
    +isMoreRestrictiveThan(other): Boolean
  }
}

' 聚合间关系
User ||--o{ UserProfile : has
User ||--o{ AuthenticationInfo : has
User }o--|| Tenant : belongs to
Role }o--|| Tenant : belongs to
UserRoleAssignment }o--|| User : assigned to
UserRoleAssignment }o--|| Role : assigns
Role ||--o{ Permission : contains

@enduml
```

**领域服务设计架构图**

```plantuml
@startuml DTF_Domain_Services

title 多租户账户管理域 - 领域服务

!define DOMAIN_SERVICE_COLOR #E6E6FA

package "领域服务层" {
  class UserRegistrationService <<Domain Service>> DOMAIN_SERVICE_COLOR {
    +registerUser(email, password, tenant): UserRegistrationResult
    +registerWithGoogle(googleToken, tenant): UserRegistrationResult
    +validateRegistration(request): ValidationResult
    +checkEmailAvailability(email, tenant): Boolean
    --
    协调多个聚合的复杂业务逻辑:
    - 检查邮箱在租户内的唯一性
    - 创建全局身份映射
    - 分配默认角色和权限
    - 发送激活邮件
    - 记录注册审计日志
  }
  
  class AuthenticationService <<Domain Service>> DOMAIN_SERVICE_COLOR {
    +authenticate(credentials, tenant): AuthenticationResult
    +authenticateWithGoogle(token, tenant): AuthenticationResult
    +refreshToken(refreshToken): TokenResult
    +logout(userId, sessionId)
    +validateSession(sessionId): SessionValidationResult
    --
    跨聚合的认证逻辑:
    - 验证用户凭据
    - 检查账户状态和权限
    - 生成JWT令牌
    - 管理会话状态
    - 记录登录审计
  }
  
  class TenantIsolationService <<Domain Service>> DOMAIN_SERVICE_COLOR {
    +isolateData(tenantId, operation): IsolationResult
    +validateTenantAccess(userId, tenantId): Boolean
    +switchTenant(userId, targetTenant): TenantSwitchResult
    +getTenantHierarchy(tenantId): TenantHierarchy
    --
    租户隔离的核心逻辑:
    - 数据访问权限控制
    - 跨租户操作验证
    - 租户层级权限继承
    - 数据隔离策略执行
  }
  
  class PermissionEvaluationService <<Domain Service>> DOMAIN_SERVICE_COLOR {
    +evaluatePermission(userId, resource, action): PermissionResult
    +getUserPermissions(userId): Set<Permission>
    +checkResourceAccess(userId, resourceId): AccessResult
    +evaluateConditionalPermission(context): Boolean
    --
    复杂权限计算逻辑:
    - 角色权限聚合计算
    - 条件权限动态评估
    - 权限继承和覆盖
    - 临时权限处理
  }
  
  class GlobalIdentityService <<Domain Service>> DOMAIN_SERVICE_COLOR {
    +createGlobalIdentity(email): GlobalUserId
    +linkTenantAccount(globalUserId, tenantAccount)
    +findGlobalIdentity(email): GlobalUserId
    +getUserTenantAccounts(globalUserId): List<TenantAccount>
    --
    全局身份管理逻辑:
    - 邮箱哈希和全局ID映射
    - 跨租户身份关联
    - 隐私保护处理
    - 身份合并和拆分
  }
}

' 领域服务依赖关系
UserRegistrationService --> GlobalIdentityService : uses
UserRegistrationService --> TenantIsolationService : uses
AuthenticationService --> PermissionEvaluationService : uses
AuthenticationService --> TenantIsolationService : uses

@enduml
```

###### *******.2 领域事件设计

```plantuml
@startuml DTF_Domain_Events

title 多租户账户管理域 - 领域事件

!define DOMAIN_EVENT_COLOR #FFE4E1

package "用户生命周期事件" {
  class UserRegisteredEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +globalUserId: GlobalUserId
    +tenantId: TenantId
    +email: EmailAddress
    +userType: UserType
    +registrationMethod: RegistrationMethod
    +occurredAt: DateTime
    --
    触发场景: 用户成功注册
    下游处理:
    - 发送欢迎邮件
    - 创建默认存储空间
    - 初始化用户偏好设置
    - 记录注册统计
  }
  
  class UserActivatedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +tenantId: TenantId
    +activatedAt: DateTime
    +activationMethod: ActivationMethod
    --
    触发场景: 用户账户激活
    下游处理:
    - 启用所有功能权限
    - 发送激活成功通知
    - 更新用户状态统计
  }
  
  class UserLoginEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +tenantId: TenantId
    +sessionId: SessionId
    +loginMethod: LoginMethod
    +clientInfo: ClientInfo
    +ipAddress: IPAddress
    +loginAt: DateTime
    --
    触发场景: 用户成功登录
    下游处理:
    - 更新最后登录时间
    - 记录登录审计日志
    - 检测异常登录行为
    - 更新活跃用户统计
  }
  
  class UserSuspendedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +tenantId: TenantId
    +suspendedBy: UserId
    +reason: SuspensionReason
    +suspendedAt: DateTime
    +suspendedUntil: DateTime
    --
    触发场景: 用户账户被暂停
    下游处理:
    - 撤销所有活跃会话
    - 发送账户暂停通知
    - 记录管理操作审计
    - 更新用户状态统计
  }
}

package "租户管理事件" {
  class TenantCreatedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +tenantId: TenantId
    +tenantCode: TenantCode
    +tenantType: TenantType
    +parentTenantId: TenantId
    +createdBy: UserId
    +createdAt: DateTime
    --
    触发场景: 新租户创建
    下游处理:
    - 初始化租户数据库
    - 创建默认角色和权限
    - 配置租户资源配额
    - 发送租户创建通知
  }
  
  class TenantConfigurationUpdatedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +tenantId: TenantId
    +configType: ConfigurationType
    +oldConfiguration: Configuration
    +newConfiguration: Configuration
    +updatedBy: UserId
    +updatedAt: DateTime
    --
    触发场景: 租户配置更新
    下游处理:
    - 应用新配置到系统
    - 通知相关用户配置变更
    - 记录配置变更审计
    - 验证配置有效性
  }
  
  class SubTenantCreatedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +parentTenantId: TenantId
    +subTenantId: TenantId
    +hierarchyLevel: Integer
    +createdBy: UserId
    +createdAt: DateTime
    --
    触发场景: 子租户创建
    下游处理:
    - 继承父租户配置
    - 设置层级权限限制
    - 更新租户层级关系
    - 通知父租户管理员
  }
}

package "权限管理事件" {
  class RoleAssignedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +roleId: RoleId
    +tenantId: TenantId
    +assignedBy: UserId
    +assignedAt: DateTime
    +expiresAt: DateTime
    --
    触发场景: 角色分配给用户
    下游处理:
    - 更新用户权限缓存
    - 发送权限变更通知
    - 记录权限分配审计
    - 刷新用户会话权限
  }
  
  class PermissionGrantedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +roleId: RoleId
    +permission: Permission
    +grantedBy: UserId
    +grantedAt: DateTime
    --
    触发场景: 权限授予角色
    下游处理:
    - 更新角色权限缓存
    - 通知拥有该角色的用户
    - 记录权限变更审计
    - 验证权限一致性
  }
  
  class PermissionRevokedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +permission: Permission
    +revokedBy: UserId
    +revokedAt: DateTime
    +reason: RevocationReason
    --
    触发场景: 权限从用户撤销
    下游处理:
    - 立即撤销相关访问
    - 清理权限相关缓存
    - 记录权限撤销审计
    - 发送权限变更通知
  }
}

package "安全事件" {
  class SecurityViolationEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +tenantId: TenantId
    +violationType: ViolationType
    +severity: SecuritySeverity
    +details: ViolationDetails
    +detectedAt: DateTime
    --
    触发场景: 检测到安全违规
    下游处理:
    - 立即安全响应措施
    - 发送安全告警通知
    - 记录安全事件日志
    - 触发安全审查流程
  }
  
  class PasswordChangedEvent <<Domain Event>> DOMAIN_EVENT_COLOR {
    +eventId: EventId
    +userId: UserId
    +tenantId: TenantId
    +changedAt: DateTime
    +changeMethod: PasswordChangeMethod
    +ipAddress: IPAddress
    --
    触发场景: 用户密码变更
    下游处理:
    - 撤销所有现有会话
    - 发送密码变更通知
    - 记录密码变更审计
    - 更新安全评分
  }
}

@enduml
```

###### *******.3 仓储接口设计

```yaml
仓储接口 (Repository Interfaces):
  UserRepository:
    - findById(userId): Optional<User>
    - findByEmail(email, tenantId): Optional<User>
    - findByGlobalUserId(globalUserId): List<User>
    - save(user): User
    - delete(userId)
    - findActiveUsersByTenant(tenantId): List<User>
  
  TenantRepository:
    - findById(tenantId): Optional<Tenant>
    - findByCode(tenantCode): Optional<Tenant>
    - findSubTenants(parentTenantId): List<Tenant>
    - save(tenant): Tenant
    - delete(tenantId)
    - findByHierarchyLevel(level): List<Tenant>
  
  RoleRepository:
    - findById(roleId): Optional<Role>
    - findByTenant(tenantId): List<Role>
    - findByUser(userId): List<Role>
    - save(role): Role
    - delete(roleId)
    - findSystemRoles(): List<Role>

工厂接口 (Factory Interfaces):
  UserFactory:
    - createUser(registrationInfo): User
    - createUserWithGoogle(googleInfo): User
    - createAdminUser(tenantId): User
  
  TenantFactory:
    - createTenant(tenantInfo): Tenant
    - createSubTenant(parentTenant, subTenantInfo): Tenant
    - createDefaultConfiguration(tenantType): TenantConfiguration
  
  RoleFactory:
    - createDefaultRoles(tenantId): List<Role>
    - createCustomRole(roleInfo): Role
    - cloneRole(sourceRole, newName): Role
```

这样的DDD设计完整覆盖了从战略设计到战术设计的所有层面，为DTF玩图平台提供了坚实的领域模型基础。

#### 7.2.3 DTF账户域微服务业务定义和流程图

```yaml
设计原则:
  - C端客户属于设备厂商的租户下
  - 一个设备厂商租户可以有多个C端客户
  - C端客户通过厂商的品牌域名注册
  - 数据隔离在厂商租户级别实现

业务场景:
  - 用户访问: brand-a.dtf.com/register
  - 系统识别: tenant_id = brand-a的租户ID
  - 用户注册: 归属到brand-a租户下
  - 数据隔离: 所有数据带有brand-a的tenant_id
  
租户类型层级:
  官方租户(tenant_type=0):
    - DTF官方运营租户
    - 系统管理和监控
  
  设备厂商租户(tenant_type=1):
    - 设备厂商的主租户
    - 品牌定制和配置管理
    - C端客户归属于此租户
  
  代理商租户(tenant_type=2):
    - 设备厂商的代理商
    - 可能有独立的C端客户群体
  
  企业客户租户(tenant_type=3):
    - 大型企业客户
    - 独立的用户管理体系


```
#### 7.2.3 DTF账户域微服务业务定义和流程图

##### 7.2.3.1 租户类型详细对比分析

###### 官方租户 (OFFICIAL_TENANT)

**🎯 业务定位与职责**：
```yaml
核心身份: DTF平台官方运营方
业务模式: 平台管理和监控
客户群体: 全平台用户和租户
盈利模式: 平台服务费和交易分成
管理范围: 全平台数据和功能
```

**📊 权限矩阵**：
```yaml
官方租户权限范围:
  用户管理: ✅ 管理所有类型用户
  租户管理: ✅ 创建/管理所有租户
  内容审核: ✅ 全平台内容审核权限
  系统配置: ✅ 全局功能开关和配置
  数据分析: ✅ 全平台数据监控分析
  财务管理: ✅ 平台收益和分成管理
  合规管理: ✅ GDPR/CCPA等法规管理
  API管理: ✅ 全平台API配额和限制
```

**🏗️ 技术特征**：
```yaml
数据隔离级别: 独立数据库集群
部署模式: 高可用集群部署
监控权限: 全平台监控和告警
安全级别: 最高安全等级
备份策略: 实时备份和异地容灾
```

###### 设备厂商租户 (DEVICE_VENDOR_TENANT)

**🎯 业务定位与职责**：
```yaml
核心身份: 设备制造商(如小米、华为等)
业务模式: 设备预装DTF应用，用户激活管理
客户群体: 购买其设备的C端消费者
盈利模式: 按设备激活数量计费
管理范围: 厂商品牌下的所有用户和数据
```

**📊 权限矩阵**：
```yaml
设备厂商租户权限范围:
  用户管理: ✅ 管理设备用户(C端客户)
  设备管理: ✅ 设备监控和激活管理
  品牌定制: ✅ UI主题和功能定制
  代理管理: ✅ 创建和管理代理商租户
  商城管理: ✅ 厂商专属商城运营
  数据分析: ✅ 设备激活和使用统计
  内容管理: ✅ 厂商图库和素材管理
  财务查看: ✅ 厂商收益和分成查看
```

**🏗️ 技术特征**：
```yaml
数据隔离级别: 数据库级隔离(大厂商)或Schema级隔离(中厂商)
部署模式: 支持SaaS和私有化部署
子域名: vendor1.dtf.com独立访问入口
API配额: 高配额(100万次/日)
存储配额: 大容量(10TB)
用户配额: 大规模(100万用户)
```

**🔄 层级关系**：
```yaml
层级结构:
  设备厂商租户(父级)
  ├── 华北代理商租户(子级)
  │   ├── 北京经销商(孙级)
  │   └── 天津经销商(孙级)
  ├── 华南代理商租户(子级)
  │   ├── 广州经销商(孙级)
  │   └── 深圳经销商(孙级)
  └── C端用户(归属用户)
      ├── 用户A(xiaomi.dtf.com注册)
      ├── 用户B(xiaomi.dtf.com注册)
      └── 用户C(xiaomi.dtf.com注册)
```

###### 代理商租户 (AGENT_TENANT)

**🎯 业务定位与职责**：
```yaml
核心身份: 区域代理商、渠道分销商
业务模式: 区域销售和用户管理
客户群体: 区域内的C端用户
盈利模式: 按交易额分成和销售佣金
管理范围: 指定区域内的用户和订单
```

**📊 权限矩阵**：
```yaml
代理商租户权限范围:
  用户管理: ✅ 区域用户管理和客服
  订单管理: ✅ 区域订单查看和处理
  销售统计: ✅ 区域销售数据分析
  佣金查看: ✅ 代理商佣金和分成
  子代理管理: ✅ 创建下级代理商
  区域配置: ✅ 区域化功能配置
  客户服务: ✅ 区域客户支持服务
  库存管理: ❌ 无库存管理权限(继承父租户)
```

**🏗️ 技术特征**：
```yaml
数据隔离级别: 行级隔离(tenant_id + region_code)
部署模式: 共享SaaS部署
父租户依赖: 必须归属于设备厂商租户
API配额: 中等配额(10万次/日)
存储配额: 中等容量(1TB)
用户配额: 中等规模(5万用户)
```

**🌍 区域化特征**：
```yaml
区域管理:
  华北代理商:
    - 管理区域: 北京、天津、河北、山西、内蒙古
    - 用户标识: region_code='华北'
    - 数据过滤: WHERE tenant_id=厂商ID AND region_code='华北'
  
  华南代理商:
    - 管理区域: 广东、广西、海南、香港、澳门
    - 用户标识: region_code='华南'
    - 数据过滤: WHERE tenant_id=厂商ID AND region_code='华南'
```

###### 企业客户租户 (ENTERPRISE_TENANT)

**🎯 业务定位与职责**：
```yaml
核心身份: 大型企业客户、图库提供商、内容服务商
业务模式: 内容创作、销售和企业服务
客户群体: 企业内部员工和外部内容消费者
盈利模式: 按内容销售分成或订阅服务费
管理范围: 企业独立的用户体系和内容库
```

**📊 权限矩阵**：
```yaml
企业客户租户权限范围:
  用户管理: ✅ 企业用户和设计师管理
  内容管理: ✅ 企业图库和素材管理
  订单管理: ✅ 企业订单和交易管理
  积分管理: ✅ 企业积分池和消费管理
  品牌定制: ✅ 企业品牌和UI定制
  数据分析: ✅ 企业业务数据分析
  API集成: ✅ 企业API和SDK集成
  财务管理: ✅ 企业收益和成本管理
```

**🏗️ 技术特征**：
```yaml
数据隔离级别: 行级隔离或独立数据库(根据规模)
部署模式: SaaS或私有化部署(可选)
独立性: 完全独立的业务体系
API配额: 根据订阅套餐(5万-50万次/日)
存储配额: 根据订阅套餐(500GB-5TB)
用户配额: 根据订阅套餐(1万-10万用户)
```

**🎨 定制化特征**：
```yaml
企业定制化:
  某图库公司:
    - 独立域名: gallery-corp.dtf.com
    - 定制UI: 企业品牌色彩和Logo
    - 专属功能: 高级图像处理工具
    - 用户体系: 设计师分级管理
    - 收益模式: 图库销售分成
  
  某设计公司:
    - 独立域名: design-studio.dtf.com
    - 定制UI: 专业设计师界面
    - 专属功能: 团队协作工具
    - 用户体系: 项目组管理
    - 收益模式: 订阅服务费
```

##### 7.2.3.2 租户类型对比总表

| 对比维度 | 官方租户 | 设备厂商租户 | 代理商租户 | 企业客户租户 |
|---------|---------|-------------|-----------|-------------|
| **业务性质** | 平台运营方 | 设备制造商 | 销售代理商 | 内容服务商 |
| **主要客户** | 全平台用户 | C端设备用户 | 区域C端用户 | 企业用户/内容消费者 |
| **数据隔离** | 独立集群 | 数据库级/Schema级 | 行级隔离 | 行级/数据库级 |
| **层级关系** | 顶级管理 | 顶级租户 | 子租户 | 独立租户 |
| **创建子租户** | ✅ 创建所有类型 | ✅ 创建代理商 | ✅ 创建下级代理 | ❌ 通常不创建 |
| **品牌定制** | ✅ 平台级定制 | ✅ 完整品牌定制 | ✅ 区域化定制 | ✅ 企业定制 |
| **计费模式** | 平台收益 | 按设备激活数 | 按交易额分成 | 按内容销售/订阅 |
| **API配额** | 无限制 | 高配额(100万/日) | 中等配额(10万/日) | 套餐配额(5-50万/日) |
| **存储配额** | 无限制 | 大容量(10TB) | 中等容量(1TB) | 套餐容量(500GB-5TB) |
| **用户配额** | 无限制 | 大规模(100万) | 中等规模(5万) | 套餐规模(1-10万) |
| **部署模式** | 高可用集群 | SaaS/私有化 | 共享SaaS | SaaS/私有化可选 |
| **监控权限** | 全平台监控 | 厂商数据监控 | 区域数据监控 | 企业数据监控 |
| **财务权限** | 全平台财务 | 厂商收益查看 | 代理佣金查看 | 企业收益管理 |

##### 7.2.3.3 租户识别和路由策略

**🌐 多维度租户识别**：
```yaml
子域名识别:
  - xiaomi.dtf.com → 小米设备厂商租户
  - huawei.dtf.com → 华为设备厂商租户
  - gallery-corp.dtf.com → 某图库企业租户
  - admin.dtf.com → DTF官方租户

路径参数识别:
  - /api/vendor/xiaomi/** → 小米厂商租户
  - /api/agent/huabei/** → 华北代理商租户
  - /api/enterprise/gallery-corp/** → 图库企业租户
  - /api/official/** → DTF官方租户

Header标识识别:
  - X-Tenant-Code: XIAOMI → 小米厂商租户
  - X-Tenant-Code: HUABEI-AGENT → 华北代理商租户
  - X-Tenant-Code: GALLERY-CORP → 图库企业租户
  - X-Tenant-Code: DTF-OFFICIAL → DTF官方租户

JWT令牌识别:
  - 令牌中包含tenant_id和tenant_type
  - 自动路由到对应租户服务
  - 权限验证和数据隔离
```

**🔒 数据隔离实现策略**：
```yaml
官方租户隔离:
  - 独立数据库集群: dtf_official_cluster
  - 全局数据访问权限
  - 跨租户数据聚合分析
  - 最高级别安全控制

设备厂商租户隔离:
  - 大厂商: 独立数据库 dtf_xiaomi_db
  - 中厂商: 独立Schema dtf_shared_db.huawei_schema
  - 小厂商: 共享表+tenant_id dtf_shared_db.shared_tables
  - 品牌级UI和功能定制

代理商租户隔离:
  - 行级隔离: WHERE tenant_id=厂商ID AND agent_id=代理商ID
  - 区域数据过滤: AND region_code='华北'
  - 继承父租户配置和限制
  - 层级权限控制

企业客户租户隔离:
  - 中大企业: 独立数据库 dtf_enterprise_gallery_db
  - 小企业: 行级隔离 WHERE tenant_id=企业ID
  - 完全独立的业务逻辑
  - 高度定制化配置
```

这样的租户类型设计既满足了不同业务模式的需求，又保证了系统的可扩展性和数据安全性！


```plantuml
@startuml DTF_Account_Domain_Business_Flow

title DTF账户域微服务业务流程图 - 基于设计模式的灵活架构

!define USER_COLOR #E1F5FE
!define SERVICE_COLOR #FFF3E0
!define DATABASE_COLOR #F3E5F5
!define EXTERNAL_COLOR #E8F5E8

actor "C端用户" as Consumer USER_COLOR
actor "设备厂商管理员" as VendorAdmin USER_COLOR
actor "DTF官方管理员" as OfficialAdmin USER_COLOR

participant "API网关\n(Gateway)" as Gateway SERVICE_COLOR
participant "用户服务\n(User Service)" as UserService SERVICE_COLOR
participant "认证服务\n(Auth Service)" as AuthService SERVICE_COLOR
participant "租户服务\n(Tenant Service)" as TenantService SERVICE_COLOR
participant "钱包服务\n(Wallet Service)" as WalletService SERVICE_COLOR

database "用户数据库\n(User DB)" as UserDB DATABASE_COLOR
database "租户数据库\n(Tenant DB)" as TenantDB DATABASE_COLOR
database "Redis缓存\n(Redis Cache)" as Redis DATABASE_COLOR

participant "Google OAuth2" as GoogleOAuth EXTERNAL_COLOR
participant "邮件服务\n(Email Service)" as EmailService EXTERNAL_COLOR
participant "审计服务\n(Audit Service)" as AuditService EXTERNAL_COLOR

== 1. C端用户注册流程 (User Registration Flow) ==

Consumer -> Gateway : 1.1 POST /api/users/commands/register\n{email, password, vendorCode}
note right of Consumer
  用户选择设备厂商并注册
  支持Google邮箱快速注册
end note

Gateway -> TenantService : 1.2 验证租户代码\nGET /api/tenants/queries/{vendorCode}/validate
TenantService -> TenantDB : 1.3 查询租户信息
TenantDB --> TenantService : 1.4 返回租户详情
TenantService --> Gateway : 1.5 租户验证结果

Gateway -> UserService : 1.6 转发注册请求\n(包含租户上下文)
UserService -> UserService : 1.7 应用策略模式\n选择注册策略(邮箱/Google)

alt 邮箱注册策略 (Email Registration Strategy)
  UserService -> UserDB : 1.8a 检查邮箱在租户内唯一性
  UserDB --> UserService : 1.9a 返回检查结果
  UserService -> UserService : 1.10a 生成用户ID和全局ID
  UserService -> UserDB : 1.11a 创建用户记录
  UserService -> EmailService : 1.12a 发送激活邮件
else Google注册策略 (Google Registration Strategy)
  UserService -> GoogleOAuth : 1.8b 验证Google Token
  GoogleOAuth --> UserService : 1.9b 返回用户信息
  UserService -> UserDB : 1.10b 创建/更新用户记录
  UserService -> UserService : 1.11b 自动激活账户
end

UserService -> WalletService : 1.13 创建用户钱包\nPOST /api/wallets/commands/create
WalletService -> UserDB : 1.14 创建钱包记录
WalletService --> UserService : 1.15 钱包创建成功

UserService -> AuditService : 1.16 记录注册审计日志
UserService --> Gateway : 1.17 返回注册结果
Gateway --> Consumer : 1.18 注册成功响应

== 2. 多端登录认证流程 (Multi-Platform Authentication Flow) ==

Consumer -> Gateway : 2.1 POST /api/auth/strategies/email/authenticate\n{email, password, vendorCode, platform}
note right of Consumer
  支持PC端、移动端、Web端
  自动识别设备指纹
end note

Gateway -> TenantService : 2.2 解析租户上下文
TenantService --> Gateway : 2.3 返回租户信息

Gateway -> AuthService : 2.4 转发认证请求
AuthService -> AuthService : 2.5 应用策略模式\n选择认证策略

alt 邮箱密码认证 (Email Password Authentication)
  AuthService -> UserDB : 2.6a 查询用户认证信息
  UserDB --> AuthService : 2.7a 返回用户数据
  AuthService -> AuthService : 2.8a 验证密码哈希
  AuthService -> AuthService : 2.9a 检查账户状态
else 设备指纹认证 (Device Fingerprint Authentication)
  AuthService -> Redis : 2.6b 查询设备指纹缓存
  Redis --> AuthService : 2.7b 返回设备信息
  AuthService -> AuthService : 2.8b 验证设备可信度
else Google OAuth认证 (Google OAuth Authentication)
  AuthService -> GoogleOAuth : 2.6c 验证Google Token
  GoogleOAuth --> AuthService : 2.7c 返回用户信息
  AuthService -> UserDB : 2.8c 查询关联用户
end

AuthService -> AuthService : 2.10 应用建造者模式\n构建JWT令牌
AuthService -> Redis : 2.11 存储会话信息
AuthService -> AuditService : 2.12 记录登录审计

AuthService --> Gateway : 2.13 返回认证结果\n{accessToken, refreshToken, userInfo}
Gateway --> Consumer : 2.14 登录成功响应

== 3. 租户管理流程 (Tenant Management Flow) ==

VendorAdmin -> Gateway : 3.1 POST /api/tenants/commands/create\n{tenantInfo, brandingConfig}
note right of VendorAdmin
  设备厂商创建租户
  支持品牌定制化配置
end note

Gateway -> AuthService : 3.2 验证管理员权限
AuthService --> Gateway : 3.3 权限验证通过

Gateway -> TenantService : 3.4 转发租户创建请求
TenantService -> TenantService : 3.5 应用模板方法模式\n标准化租户创建流程

TenantService -> TenantService : 3.6 生成租户代码和域名
TenantService -> TenantDB : 3.7 创建租户记录
TenantService -> TenantService : 3.8 应用建造者模式\n构建租户配置

TenantService -> TenantService : 3.9 初始化默认角色和权限
TenantService -> UserService : 3.10 创建租户管理员账户
UserService -> UserDB : 3.11 创建管理员用户
UserService --> TenantService : 3.12 管理员创建成功

TenantService -> AuditService : 3.13 记录租户创建审计
TenantService --> Gateway : 3.14 返回租户创建结果
Gateway --> VendorAdmin : 3.15 租户创建成功

== 4. 用户档案管理流程 (User Profile Management Flow) ==

Consumer -> Gateway : 4.1 PUT /api/users/commands/{userId}/profile\n{profileData}
note right of Consumer
  用户更新个人信息
  支持头像上传和隐私设置
end note

Gateway -> AuthService : 4.2 验证用户身份和权限
AuthService --> Gateway : 4.3 身份验证通过

Gateway -> UserService : 4.4 转发档案更新请求
UserService -> UserService : 4.5 应用装饰器模式\n添加数据验证和加密

UserService -> UserService : 4.6 验证数据格式和完整性
UserService -> UserService : 4.7 加密敏感信息(真实姓名、地址)
UserService -> UserDB : 4.8 更新用户档案
UserDB --> UserService : 4.9 更新成功确认

UserService -> Redis : 4.10 更新用户缓存
UserService -> AuditService : 4.11 记录档案变更审计
UserService --> Gateway : 4.12 返回更新结果
Gateway --> Consumer : 4.13 档案更新成功

== 5. 钱包积分管理流程 (Wallet Points Management Flow) ==

Consumer -> Gateway : 5.1 POST /api/wallets/commands/{walletId}/points/add\n{amount, source, description}
note right of Consumer
  用户积分充值或获得奖励
  支持多种积分来源
end note

Gateway -> AuthService : 5.2 验证用户身份
AuthService --> Gateway : 5.3 身份验证通过

Gateway -> WalletService : 5.4 转发积分操作请求
WalletService -> WalletService : 5.5 应用账户模式\n确保复式记账

WalletService -> UserDB : 5.6 开始数据库事务
WalletService -> WalletService : 5.7 验证钱包状态和余额
WalletService -> WalletService : 5.8 计算新余额和流水记录

WalletService -> UserDB : 5.9 更新钱包余额
WalletService -> UserDB : 5.10 创建积分流水记录
WalletService -> UserDB : 5.11 提交事务

WalletService -> Redis : 5.12 更新余额缓存
WalletService -> AuditService : 5.13 记录积分操作审计
WalletService --> Gateway : 5.14 返回操作结果
Gateway --> Consumer : 5.15 积分操作成功

== 6. 官方管理员审核流程 (Official Admin Audit Flow) ==

OfficialAdmin -> Gateway : 6.1 GET /api/users/queries/search?status=PENDING_AUDIT
note right of OfficialAdmin
  官方管理员查看待审核用户
  支持批量审核操作
end note

Gateway -> AuthService : 6.2 验证官方管理员权限
AuthService --> Gateway : 6.3 超级管理员权限确认

Gateway -> UserService : 6.4 查询待审核用户列表
UserService -> UserDB : 6.5 查询用户数据(跨租户)
UserDB --> UserService : 6.6 返回用户列表
UserService --> Gateway : 6.7 返回查询结果
Gateway --> OfficialAdmin : 6.8 显示待审核用户

OfficialAdmin -> Gateway : 6.9 PUT /api/users/commands/{userId}/status\n{status: "APPROVED", reason: "审核通过"}

Gateway -> UserService : 6.10 转发审核请求
UserService -> UserService : 6.11 应用状态模式\n管理用户状态转换

UserService -> UserDB : 6.12 更新用户状态
UserService -> EmailService : 6.13 发送审核结果通知
UserService -> AuditService : 6.14 记录审核操作日志

UserService --> Gateway : 6.15 返回审核结果
Gateway --> OfficialAdmin : 6.16 审核操作完成

== 7. 跨租户身份统一流程 (Cross-Tenant Identity Unification Flow) ==

Consumer -> Gateway : 7.1 POST /api/users/commands/switch-tenant\n{targetVendorCode}
note right of Consumer
  用户切换到另一个设备厂商
  保持全局身份统一
end note

Gateway -> AuthService : 7.2 验证当前用户身份
AuthService --> Gateway : 7.3 身份验证通过

Gateway -> UserService : 7.4 转发租户切换请求
UserService -> UserService : 7.5 应用适配器模式\n适配不同租户规则

UserService -> UserDB : 7.6 查询用户全局身份
UserService -> TenantService : 7.7 验证目标租户状态
TenantService --> UserService : 7.8 租户验证通过

UserService -> UserDB : 7.9 检查目标租户下是否已有账户
alt 已有账户 (Existing Account)
  UserService -> UserService : 7.10a 关联现有账户
  UserService -> AuthService : 7.11a 生成新租户上下文令牌
else 新建账户 (New Account)
  UserService -> UserDB : 7.10b 在目标租户下创建新账户
  UserService -> WalletService : 7.11b 创建新钱包
  UserService -> AuthService : 7.12b 生成新租户上下文令牌
end

UserService -> AuditService : 7.13 记录租户切换审计
UserService --> Gateway : 7.14 返回切换结果
Gateway --> Consumer : 7.15 租户切换成功

@enduml
```

##### 7.2.3.4 设计模式在账户域微服务中的应用架构图

```plantuml
@startuml DTF_Account_Design_Patterns_Architecture

title DTF账户域微服务 - 设计模式应用架构图

!define PATTERN_COLOR #E1F5FE
!define SERVICE_COLOR #FFF3E0
!define COMPONENT_COLOR #F3E5F5

package "策略模式层 (Strategy Pattern Layer)" PATTERN_COLOR {
  interface "认证策略接口\n(AuthenticationStrategy)" as AuthStrategy {
    + authenticate(credentials: Credentials) : AuthResult
    + validateCredentials(credentials: Credentials) : Boolean
    + getAuthType() : AuthType
  }
  
  class "邮箱密码策略\n(EmailPasswordStrategy)" as EmailStrategy {
    + authenticate(emailCredentials: EmailCredentials) : AuthResult
    + validatePassword(password: String, hash: String) : Boolean
    + checkAccountLockout(userId: UserId) : Boolean
  }
  
  class "Google OAuth策略\n(GoogleOAuthStrategy)" as GoogleStrategy {
    + authenticate(googleToken: GoogleToken) : AuthResult
    + validateGoogleToken(token: String) : GoogleUserInfo
    + linkGoogleAccount(userId: UserId, googleId: String)
  }
  
  class "设备指纹策略\n(DeviceFingerprintStrategy)" as DeviceStrategy {
    + authenticate(deviceFingerprint: DeviceFingerprint) : AuthResult
    + validateDeviceTrust(fingerprint: String) : TrustLevel
    + updateDeviceHistory(userId: UserId, device: Device)
  }
  
  class "认证上下文\n(AuthenticationContext)" as AuthContext {
    + strategy: AuthenticationStrategy
    + executeAuthentication(credentials: Credentials) : AuthResult
    + setStrategy(strategy: AuthenticationStrategy)
  }
}

package "建造者模式层 (Builder Pattern Layer)" PATTERN_COLOR {
  class "JWT令牌建造者\n(JWTTokenBuilder)" as JWTBuilder {
    + setUserId(userId: UserId) : JWTTokenBuilder
    + setTenantId(tenantId: TenantId) : JWTTokenBuilder
    + setPermissions(permissions: List<Permission>) : JWTTokenBuilder
    + setExpirationTime(expiry: DateTime) : JWTTokenBuilder
    + setDeviceInfo(device: DeviceInfo) : JWTTokenBuilder
    + build() : JWTToken
  }
  
  class "租户配置建造者\n(TenantConfigBuilder)" as TenantConfigBuilder {
    + setBranding(branding: BrandingConfig) : TenantConfigBuilder
    + setFeatures(features: FeatureConfig) : TenantConfigBuilder
    + setLimits(limits: ResourceLimits) : TenantConfigBuilder
    + setDeployment(deployment: DeploymentConfig) : TenantConfigBuilder
    + build() : TenantConfig
  }
  
  class "用户档案建造者\n(UserProfileBuilder)" as UserProfileBuilder {
    + setBasicInfo(name: String, avatar: String) : UserProfileBuilder
    + setContactInfo(phone: String, email: String) : UserProfileBuilder
    + setPreferences(preferences: UserPreferences) : UserProfileBuilder
    + setPrivacySettings(privacy: PrivacySettings) : UserProfileBuilder
    + build() : UserProfile
  }
}

package "模板方法模式层 (Template Method Pattern Layer)" PATTERN_COLOR {
  abstract class "租户创建模板\n(TenantCreationTemplate)" as TenantTemplate {
    + createTenant(tenantInfo: TenantInfo) : Tenant
    # validateTenantInfo(info: TenantInfo) : ValidationResult
    # generateTenantCode(info: TenantInfo) : String
    # createTenantDatabase(tenant: Tenant) : Database
    # initializeDefaultRoles(tenant: Tenant) : List<Role>
    # setupTenantConfiguration(tenant: Tenant, config: TenantConfig)
    # sendWelcomeNotification(tenant: Tenant)
  }
  
  class "设备厂商租户创建\n(DeviceVendorTenantCreation)" as DeviceVendorCreation {
    # validateTenantInfo(info: TenantInfo) : ValidationResult
    # initializeDefaultRoles(tenant: Tenant) : List<Role>
    # setupTenantConfiguration(tenant: Tenant, config: TenantConfig)
  }
  
  class "代理商租户创建\n(AgentTenantCreation)" as AgentCreation {
    # validateTenantInfo(info: TenantInfo) : ValidationResult
    # initializeDefaultRoles(tenant: Tenant) : List<Role>
    # setupTenantConfiguration(tenant: Tenant, config: TenantConfig)
  }
}

package "装饰器模式层 (Decorator Pattern Layer)" PATTERN_COLOR {
  interface "用户操作接口\n(UserOperation)" as UserOperation {
    + execute(user: User, operation: Operation) : OperationResult
  }
  
  class "基础用户操作\n(BaseUserOperation)" as BaseUserOperation {
    + execute(user: User, operation: Operation) : OperationResult
  }
  
  class "数据验证装饰器\n(ValidationDecorator)" as ValidationDecorator {
    - operation: UserOperation
    + execute(user: User, operation: Operation) : OperationResult
    - validateInput(operation: Operation) : ValidationResult
  }
  
  class "数据加密装饰器\n(EncryptionDecorator)" as EncryptionDecorator {
    - operation: UserOperation
    + execute(user: User, operation: Operation) : OperationResult
    - encryptSensitiveData(data: SensitiveData) : EncryptedData
  }
  
  class "审计日志装饰器\n(AuditLogDecorator)" as AuditDecorator {
    - operation: UserOperation
    + execute(user: User, operation: Operation) : OperationResult
    - logOperation(user: User, operation: Operation, result: OperationResult)
  }
}

package "适配器模式层 (Adapter Pattern Layer)" PATTERN_COLOR {
  interface "租户规则接口\n(TenantRuleInterface)" as TenantRuleInterface {
    + validateUserRegistration(user: User) : ValidationResult
    + applyBusinessRules(operation: BusinessOperation) : RuleResult
    + getCustomConfiguration() : CustomConfig
  }
  
  class "标准租户规则\n(StandardTenantRules)" as StandardRules {
    + validateUserRegistration(user: User) : ValidationResult
    + applyBusinessRules(operation: BusinessOperation) : RuleResult
    + getCustomConfiguration() : CustomConfig
  }
  
  class "设备厂商规则适配器\n(DeviceVendorRuleAdapter)" as DeviceVendorAdapter {
    - standardRules: StandardTenantRules
    - vendorSpecificRules: VendorSpecificRules
    + validateUserRegistration(user: User) : ValidationResult
    + applyBusinessRules(operation: BusinessOperation) : RuleResult
    + getCustomConfiguration() : CustomConfig
  }
  
  class "代理商规则适配器\n(AgentRuleAdapter)" as AgentAdapter {
    - standardRules: StandardTenantRules
    - agentSpecificRules: AgentSpecificRules
    + validateUserRegistration(user: User) : ValidationResult
    + applyBusinessRules(operation: BusinessOperation) : RuleResult
    + getCustomConfiguration() : CustomConfig
  }
}

package "状态模式层 (State Pattern Layer)" PATTERN_COLOR {
  interface "用户状态接口\n(UserState)" as UserState {
    + login(user: User) : LoginResult
    + updateProfile(user: User, profile: UserProfile) : UpdateResult
    + performOperation(user: User, operation: Operation) : OperationResult
    + getAvailableActions() : List<Action>
  }
  
  class "激活状态\n(ActiveState)" as ActiveState {
    + login(user: User) : LoginResult
    + updateProfile(user: User, profile: UserProfile) : UpdateResult
    + performOperation(user: User, operation: Operation) : OperationResult
    + getAvailableActions() : List<Action>
  }
  
  class "暂停状态\n(SuspendedState)" as SuspendedState {
    + login(user: User) : LoginResult
    + updateProfile(user: User, profile: UserProfile) : UpdateResult
    + performOperation(user: User, operation: Operation) : OperationResult
    + getAvailableActions() : List<Action>
  }
  
  class "锁定状态\n(LockedState)" as LockedState {
    + login(user: User) : LoginResult
    + updateProfile(user: User, profile: UserProfile) : UpdateResult
    + performOperation(user: User, operation: Operation) : OperationResult
    + getAvailableActions() : List<Action>
  }
  
  class "用户上下文\n(UserContext)" as UserContext {
    - currentState: UserState
    + setState(state: UserState)
    + login() : LoginResult
    + updateProfile(profile: UserProfile) : UpdateResult
    + performOperation(operation: Operation) : OperationResult
  }
}

package "账户模式层 (Account Pattern Layer)" PATTERN_COLOR {
  class "钱包账户\n(WalletAccount)" as WalletAccount {
    + accountId: AccountId
    + userId: UserId
    + balance: Money
    + currency: Currency
    + status: AccountStatus
    + transactions: List<Transaction>
    --
    + debit(amount: Money, description: String) : Transaction
    + credit(amount: Money, description: String) : Transaction
    + transfer(targetAccount: WalletAccount, amount: Money) : TransferResult
    + freeze() : FreezeResult
    + unfreeze() : UnfreezeResult
    + getBalance() : Money
    + getTransactionHistory() : List<Transaction>
  }
  
  class "积分账户\n(PointsAccount)" as PointsAccount {
    + accountId: AccountId
    + userId: UserId
    + points: Integer
    + earnedPoints: Integer
    + spentPoints: Integer
    + expiringPoints: Map<Date, Integer>
    --
    + earnPoints(points: Integer, source: PointSource) : Transaction
    + spendPoints(points: Integer, purpose: PointPurpose) : Transaction
    + transferPoints(targetAccount: PointsAccount, points: Integer) : TransferResult
    + getAvailablePoints() : Integer
    + getExpiringPoints(days: Integer) : Integer
  }
  
  class "交易记录\n(Transaction)" as Transaction {
    + transactionId: TransactionId
    + accountId: AccountId
    + type: TransactionType
    + amount: Money
    + description: String
    + timestamp: DateTime
    + status: TransactionStatus
    + metadata: Map<String, Object>
    --
    + reverse() : Transaction
    + getDisplayAmount() : String
    + isReversible() : Boolean
  }
}

' 关系定义
AuthStrategy <|-- EmailStrategy
AuthStrategy <|-- GoogleStrategy
AuthStrategy <|-- DeviceStrategy
AuthContext --> AuthStrategy

TenantTemplate <|-- DeviceVendorCreation
TenantTemplate <|-- AgentCreation

UserOperation <|-- BaseUserOperation
UserOperation <|-- ValidationDecorator
UserOperation <|-- EncryptionDecorator
UserOperation <|-- AuditDecorator
ValidationDecorator --> UserOperation
EncryptionDecorator --> UserOperation
AuditDecorator --> UserOperation

TenantRuleInterface <|-- StandardRules
TenantRuleInterface <|-- DeviceVendorAdapter
TenantRuleInterface <|-- AgentAdapter
DeviceVendorAdapter --> StandardRules
AgentAdapter --> StandardRules

UserState <|-- ActiveState
UserState <|-- SuspendedState
UserState <|-- LockedState
UserContext --> UserState

WalletAccount --> Transaction
PointsAccount --> Transaction

@enduml
```

##### 7.2.3.5 账户域微服务API设计模式应用总结

```yaml
设计模式应用效果分析:

策略模式 (Strategy Pattern):
  应用场景: 多种认证方式(邮箱、Google、设备指纹)
  业务价值: 灵活支持新认证方式，降低耦合度
  技术优势: 运行时动态切换认证策略，易于扩展

建造者模式 (Builder Pattern):
  应用场景: JWT令牌构建、租户配置构建、用户档案构建
  业务价值: 复杂对象创建过程标准化，参数验证集中化
  技术优势: 链式调用提升代码可读性，参数校验统一管理

模板方法模式 (Template Method Pattern):
  应用场景: 租户创建流程标准化
  业务价值: 确保租户创建流程一致性，支持个性化定制
  技术优势: 算法骨架固定，具体实现可变，易于维护

装饰器模式 (Decorator Pattern):
  应用场景: 用户操作的数据验证、加密、审计
  业务价值: 横切关注点分离，功能组合灵活
  技术优势: 单一职责原则，功能可插拔，易于测试

适配器模式 (Adapter Pattern):
  应用场景: 不同租户的个性化业务规则适配
  业务价值: 支持租户个性化需求，保持系统统一性
  技术优势: 接口统一，实现多样，降低系统复杂度

状态模式 (State Pattern):
  应用场景: 用户状态管理(激活、暂停、锁定)
  业务价值: 状态转换规则清晰，操作权限精确控制
  技术优势: 状态转换逻辑封装，避免复杂条件判断

账户模式 (Account Pattern):
  应用场景: 钱包和积分的复式记账管理
  业务价值: 资金安全保证，交易记录完整
  技术优势: 数据一致性保证，支持事务回滚

```

##### 7.2.3.6 账户域微服务业务流程图的设计亮点:

```yaml
  完整业务闭环:
  - 从用户注册→认证→租户管理→档案管理→钱包管理→审核管理→跨租户切换，形成完整业务链路 
  设计模式深度应用: 
    - 策略模式: 支持多种认证方式的灵活切换
    - 建造者模式: 复杂对象构建的标准化和参数验证
    - 模板方法模式: 租户创建流程的标准化和个性化平衡
    - 装饰器模式: 横切关注点的优雅分离
    - 适配器模式: 多租户个性化需求的统一适配
    - 状态模式: 用户状态转换的规则化管理
    - 账户模式: 金融级别的资金安全保证
  多租户架构优势: 
    - 数据隔离: 租户级别的完全数据隔离
    - 身份统一: 全局身份标识支持跨租户切换
    - 个性化定制: 支持租户级别的品牌和功能定制
  技术架构特色: 
    - CQRS分离: 命令查询职责分离，提升性能
    - 异步审计: 操作审计异步化，不影响主流程
    - 缓存策略: Redis缓存提升响应速度
    - 事务保证: 关键操作的数据一致性保证
```

### 7.3 AWS云原生架构

#### 7.3.1 计算资源

```yaml
Amazon ECS:
  - 微服务容器化部署
  - 自动扩缩容
  - 负载均衡
  - 健康检查

AWS Lambda:
  - AI图像处理
  - 事件驱动任务
  - 无服务器计算
  - 按需付费

Amazon EC2:
  - 高性能计算需求
  - GPU实例(AI训练)
  - 自定义环境
```

#### 7.3.2 存储服务

```yaml
Amazon RDS:
  - MySQL主从集群
  - 自动备份
  - 多可用区部署
  - 读写分离

Amazon S3:
  - 对象存储
  - CDN分发
  - 版本控制
  - 生命周期管理

Amazon ElastiCache:
  - Redis集群
  - 内存缓存
  - 会话存储
  - 分布式锁
```

#### 7.3.3 网络安全

```yaml
Amazon VPC:
  - 私有网络
  - 子网隔离
  - 安全组
  - 网络ACL

AWS WAF:
  - Web应用防火墙
  - DDoS防护
  - SQL注入防护
  - XSS防护

AWS Certificate Manager:
  - SSL/TLS证书
  - 自动续期
  - 域名验证
```

### 7.4 监控与运维

#### 7.4.1 SkyWalking + AWS混合监控

```plantuml
@startuml DTF_Monitoring_Architecture

title DTF监控架构 - SkyWalking与AWS原生混合

package "应用监控层" {
  [SkyWalking Agent] as Agent
  [SkyWalking OAP] as OAP
  [SkyWalking UI] as UI
}

package "AWS原生监控" {
  [CloudWatch] as CW
  [X-Ray] as XRay
  [CloudTrail] as CT
}

package "自定义监控" {
  [Prometheus] as Prom
  [Grafana] as Graf
  [AlertManager] as Alert
}

package "日志聚合" {
  [ELK Stack] as ELK
  [Fluentd] as Fluent
}

package "微服务集群" {
  [用户服务] as UserSvc
  [设备服务] as DeviceSvc
  [图库服务] as GallerySvc
  [商城服务] as MallSvc
}

' 监控数据流
UserSvc --> Agent
DeviceSvc --> Agent
GallerySvc --> Agent
MallSvc --> Agent

Agent --> OAP
OAP --> UI

UserSvc --> CW
DeviceSvc --> XRay
GallerySvc --> CT

UserSvc --> Prom
Prom --> Graf
Graf --> Alert

UserSvc --> Fluent
Fluent --> ELK

' 告警通知
Alert --> [Slack]
Alert --> [Email]
Alert --> [SMS]

@enduml
```

#### 7.4.2 监控指标体系

```yaml
业务指标:
  - 用户注册转化率
  - 设备在线率
  - 图库下载量
  - 订单成功率
  - AI处理成功率

技术指标:
  - 服务响应时间
  - 服务可用性
  - 错误率
  - 吞吐量
  - 资源使用率

基础设施指标:
  - CPU使用率
  - 内存使用率
  - 磁盘I/O
  - 网络带宽
  - 数据库连接数
```

## 🔒 8. 合规性设计 - 基于美国CCPA、GDPR、SOX法规

### 8.1 法规要求分析

#### 8.1.1 GDPR (欧盟通用数据保护条例)

```yaml
核心原则:
  数据最小化: 只收集必要的个人数据
  目的限制: 明确数据处理目的
  存储限制: 限制数据保留时间
  准确性: 确保数据准确和最新
  完整性和保密性: 数据安全保护
  问责制: 证明合规性

数据主体权利:
  访问权: 用户可查看其个人数据
  更正权: 用户可修正错误数据
  删除权: 用户可要求删除数据
  限制处理权: 用户可限制数据处理
  数据可携带权: 用户可导出数据
  反对权: 用户可反对数据处理

技术实现:
  同意管理: dtf_data_processing_consent表
  数据加密: 敏感数据AES-256加密
  访问控制: 基于角色的权限管理
  审计日志: 完整的操作记录
  数据匿名化: 个人标识符移除
```

#### 8.1.2 CCPA (加州消费者隐私法案)

```yaml
核心要求:
  透明度: 明确告知数据收集和使用
  选择权: 消费者可选择退出数据销售
  访问权: 消费者可访问其个人信息
  删除权: 消费者可要求删除数据
  非歧视: 不因行使权利而歧视

个人信息类别:
  标识符: 姓名、邮箱、IP地址等
  商业信息: 购买记录、消费偏好
  生物识别信息: 指纹、面部识别
  网络活动: 浏览历史、搜索记录
  地理位置: GPS坐标、位置数据

技术实现:
  隐私政策: 详细的隐私声明
  选择退出: 一键退出数据销售
  数据清单: 完整的数据收集记录
  响应机制: 30天内响应用户请求
  第三方管理: 严格的数据共享控制
```

#### 8.1.3 SOX (萨班斯-奥克斯利法案)

```yaml
核心要求:
  内部控制: 建立有效的内部控制制度
  财务报告: 准确的财务信息披露
  审计独立: 独立的外部审计
  管理责任: 管理层对财务报告负责

技术控制:
  访问控制: 严格的系统访问权限
  变更管理: 规范的系统变更流程
  数据完整性: 确保数据准确性
  备份恢复: 可靠的数据备份机制
  审计追踪: 完整的操作审计日志

实施措施:
  职责分离: 开发、测试、生产环境分离
  双重验证: 关键操作需要双人确认
  定期审计: 定期的内部和外部审计
  文档管理: 完整的制度和流程文档
  培训教育: 定期的合规培训
```

### 8.2 合规架构设计

#### 8.2.1 数据分类与保护

```plantuml
@startuml DTF_Data_Classification

title DTF数据分类与保护架构

package "数据分类" {
  [公开数据] as PublicData
  [内部数据] as InternalData
  [敏感数据] as SensitiveData
  [机密数据] as ConfidentialData
}

package "保护措施" {
  [访问控制] as AccessControl
  [数据加密] as Encryption
  [数据脱敏] as Masking
  [审计日志] as AuditLog
}

package "合规要求" {
  [GDPR合规] as GDPR
  [CCPA合规] as CCPA
  [SOX合规] as SOX
}

PublicData --> AccessControl
InternalData --> AccessControl
SensitiveData --> Encryption
ConfidentialData --> Encryption

SensitiveData --> Masking
ConfidentialData --> Masking

AccessControl --> AuditLog
Encryption --> AuditLog
Masking --> AuditLog

AuditLog --> GDPR
AuditLog --> CCPA
AuditLog --> SOX

@enduml
```

#### 8.2.2 隐私保护技术实现

```yaml
数据加密:
  传输加密: TLS 1.3端到端加密
  存储加密: AES-256数据库加密
  字段加密: 敏感字段单独加密
  密钥管理: AWS KMS密钥管理

数据脱敏:
  邮箱哈希: SHA-256不可逆哈希
  手机脱敏: 保留前3位和后4位
  IP地址: 保留前3段，最后段置0
  姓名脱敏: 保留姓氏，名字用*替代

访问控制:
  RBAC模型: 基于角色的访问控制
  ABAC模型: 基于属性的访问控制
  最小权限: 最小必要权限原则
  权限审计: 定期权限审查

数据生命周期:
  收集阶段: 明确收集目的和法律依据
  处理阶段: 按照声明目的处理数据
  存储阶段: 安全存储和访问控制
  共享阶段: 严格的第三方共享控制
  删除阶段: 按照保留政策删除数据
```

### 8.3 合规监控与审计

#### 8.3.1 审计日志系统

```yaml
日志类型:
  访问日志: 用户访问系统的记录
  操作日志: 用户操作行为的记录
  系统日志: 系统运行状态的记录
  安全日志: 安全事件的记录
  合规日志: 合规相关操作的记录

日志内容:
  时间戳: 精确到毫秒的时间记录
  用户标识: 操作用户的唯一标识
  操作类型: 具体的操作类型
  资源对象: 操作的资源对象
  操作结果: 操作成功或失败
  IP地址: 操作来源IP地址(脱敏)
  用户代理: 浏览器或客户端信息

日志保护:
  完整性: 日志不可篡改
  可用性: 日志可靠存储
  保密性: 敏感信息脱敏
  可追溯: 完整的操作链路
```

#### 8.3.2 合规报告生成

```yaml
GDPR报告:
  数据处理活动记录
  数据主体权利响应报告
  数据泄露事件报告
  第三方数据处理协议

CCPA报告:
  个人信息收集报告
  消费者权利请求处理报告
  数据销售和共享报告
  隐私政策更新记录

SOX报告:
  内部控制评估报告
  IT一般控制测试报告
  应用控制测试报告
  管理层认证报告
```

## 🎨 9. 图库社区引入规划

### 9.1 图库系统设计

#### 9.1.1 图库内容管理

```yaml
内容分类:
  设计模板: 海报、横幅、卡片、标志等
  素材资源: 图标、插画、纹理、背景等
  字体资源: 中英文字体、艺术字体等
  3D模型: 雕刻模型、打印模型等

内容属性:
  基础信息: 标题、描述、分类、标签
  技术参数: 分辨率、格式、大小、DPI
  商业属性: 价格、许可类型、版权信息
  质量指标: 下载量、评分、收藏数

内容审核:
  自动审核: AI识别违规内容
  人工审核: 专业审核员复查
  社区举报: 用户举报机制
  版权保护: 原创性检测
```

#### 9.1.2 图库商业化模式

```yaml
定价策略:
  免费内容: 官方提供的基础素材
  付费内容: 高质量设计模板和素材
  订阅模式: 月度/年度无限下载
  积分购买: 使用积分购买单个内容

收益分成:
  官方内容: 100%官方收益
  厂商内容: 70%厂商 + 30%平台
  用户内容: 60%创作者 + 40%平台
  代理内容: 50%代理 + 20%厂商 + 30%平台

版权管理:
  原创保护: 原创内容版权保护
  授权管理: 明确的使用授权范围
  侵权处理: 快速的侵权投诉处理
  法律支持: 必要时提供法律支持
```

### 9.2 社区系统设计

#### 9.2.1 社区功能模块

```yaml
内容发布:
  作品展示: 用户作品展示和分享
  教程分享: 设计教程和技巧分享
  问题求助: 技术问题求助和解答
  讨论交流: 自由讨论和交流

互动功能:
  点赞收藏: 对内容的点赞和收藏
  评论回复: 多层级评论和回复
  关注粉丝: 用户关注和粉丝系统
  私信聊天: 用户间私信功能

内容管理:
  分类标签: 内容分类和标签管理
  搜索发现: 强大的内容搜索功能
  推荐算法: 个性化内容推荐
  热门排行: 热门内容排行榜
```

#### 9.2.2 社区运营策略

```yaml
用户激励:
  积分奖励: 发布内容获得积分奖励
  等级系统: 用户等级和特权系统
  徽章成就: 各种成就徽章系统
  排行榜: 创作者排行榜

内容质量:
  优质内容: 优质内容推荐和奖励
  原创保护: 原创内容保护机制
  抄袭检测: 自动抄袭检测系统
  质量评分: 内容质量评分系统

社区治理:
  行为规范: 明确的社区行为规范
  违规处理: 违规内容和用户处理
  申诉机制: 用户申诉和复议机制
  版主管理: 社区版主管理制度
```

#### 9.2.1 社区功能模块

基于DTF玩图平台的多角色用户体系，设计完整的社区功能架构:

```plantuml
@startuml DTF_Community_System_Architecture

title DTF玩图平台社区系统架构 - 多角色互动生态

package "社区核心功能" {
  package "内容发布模块" {
    [作品展示] as WorkDisplay
    [教程分享] as TutorialShare
    [问题求助] as HelpRequest
    [讨论交流] as Discussion
    [资源交流] as ResourceShare
  }
  
  package "互动功能模块" {
    [点赞收藏] as LikeCollect
    [评论回复] as CommentReply
    [关注粉丝] as FollowFan
    [私信聊天] as PrivateMessage
    [分享转发] as ShareForward
  }
  
  package "内容管理模块" {
    [分类标签] as CategoryTag
    [搜索发现] as SearchDiscover
    [推荐算法] as RecommendAlgo
    [热门排行] as HotRanking
    [内容审核] as ContentReview
  }
}

package "角色权限体系" {
  actor [C端用户] as CUser
  actor [设备厂商] as DeviceVendor
  actor [代理商] as Agent
  actor [官方管理] as Official
}

package "业务集成模块" {
  [图库集成] as GalleryIntegration
  [商城集成] as MallIntegration
  [AI工具集成] as AIIntegration
  [设备数据集成] as DeviceIntegration
}

' 用户权限关系
CUser --> WorkDisplay : 发布作品
CUser --> TutorialShare : 分享教程
CUser --> HelpRequest : 求助问题
CUser --> Discussion : 参与讨论
CUser --> ResourceShare : 交流资源

DeviceVendor --> WorkDisplay : 发布官方作品
DeviceVendor --> TutorialShare : 发布官方教程
DeviceVendor --> Discussion : 技术支持

Official --> ContentReview : 内容审核
Official --> Discussion : 官方公告

' 功能交互关系
WorkDisplay --> LikeCollect
TutorialShare --> CommentReply
HelpRequest --> CommentReply
Discussion --> FollowFan

' 业务集成关系
WorkDisplay --> GalleryIntegration : 作品上架图库
TutorialShare --> AIIntegration : AI工具教程
ResourceShare --> MallIntegration : 耗材推荐
Discussion --> DeviceIntegration : 设备使用技巧

@enduml
```

**社区功能详细设计**:

```yaml
内容发布功能:
  作品展示:
    - 设计作品图片/视频展示
    - 制作过程记录和分享
    - 使用设备和材料标注
    - 制作时间和难度标记
    - 作品标签和分类管理
  
  教程分享:
    - 图文教程发布
    - 视频教程上传
    - 分步骤制作指南
    - 技巧和窍门分享
    - 常见问题解答
  
  问题求助:
    - 技术问题提问
    - 设备故障求助
    - 设计思路咨询
    - 材料选择建议
    - 专家答疑互动
  
  讨论交流:
    - 自由话题讨论
    - 行业趋势分析
    - 新品发布讨论
    - 用户体验分享
    - 创意灵感交流
  
  资源交流:
    - 设计素材分享
    - 设备使用技巧
    - 材料采购信息
    - 工具推荐评测
    - 行业资讯分享

互动功能设计:
  点赞收藏:
    - 多级点赞系统（👍❤️🔥）
    - 个人收藏夹管理
    - 收藏分类和标签
    - 收藏内容推荐
    - 收藏数据统计
  
  评论回复:
    - 多层级评论系统
    - 富文本评论支持
    - 图片评论功能
    - @用户提醒功能
    - 评论点赞和举报
  
  关注粉丝:
    - 用户关注系统
    - 粉丝动态推送
    - 关注分组管理
    - 互相关注识别
    - 关注数据分析
  
  私信聊天:
    - 一对一私信功能
    - 图片文件发送
    - 消息已读状态
    - 消息历史记录
    - 骚扰消息过滤

内容管理功能:
  分类标签:
    - 内容智能分类
    - 用户自定义标签
    - 热门标签推荐
    - 标签搜索优化
    - 标签数据分析
  
  搜索发现:
    - 全文搜索功能
    - 图像识别搜索
    - 高级筛选条件
    - 搜索历史记录
    - 搜索结果优化
  
  推荐算法:
    - 基于用户兴趣推荐
    - 协同过滤推荐
    - 内容相似度推荐
    - 热度趋势推荐
    - 个性化推荐
  
  热门排行:
    - 实时热门内容
    - 周/月热门榜单
    - 分类热门排行
    - 新人作品推荐
    - 专家推荐内容
```

#### 9.2.2 社区运营策略

```yaml
用户激励体系:
  积分奖励机制:
    - 发布作品: +10积分
    - 发布教程: +20积分
    - 回答问题: +5积分
    - 获得点赞: +1积分
    - 每日签到: +2积分
    - 完成任务: +5-50积分
  
  等级系统设计:
    - 新手创作者 (0-100积分)
    - 进阶设计师 (101-500积分)
    - 资深创作者 (501-2000积分)
    - 专业设计师 (2001-5000积分)
    - 大师级创作者 (5000+积分)
  
  徽章成就系统:
    - 创作徽章: 首次发布、百赞作品、千赞作品
    - 互动徽章: 热心助人、评论达人、点赞之王
    - 技能徽章: 激光雕刻专家、3D打印达人、设计大师
    - 时间徽章: 连续签到、老用户、活跃用户
  
  排行榜激励:
    - 创作者月度排行
    - 教程分享排行
    - 互助问答排行
    - 新人推荐排行

内容质量保障:
  优质内容识别:
    - AI智能评分系统
    - 用户投票评价
    - 专家人工评审
    - 数据指标综合评估
  
  原创保护机制:
    - 图像指纹识别
    - 内容相似度检测
    - 原创声明系统
    - 版权投诉处理
  
  抄袭检测系统:
    - 自动抄袭检测
    - 人工审核确认
    - 抄袭行为处罚
    - 原创者权益保护
  
  质量评分体系:
    - 内容原创性评分
    - 技术难度评分
    - 教学价值评分
    - 用户反馈评分

社区治理体系:
  行为规范制定:
    - 社区公约和准则
    - 内容发布规范
    - 互动行为准则
    - 商业行为规范
  
  违规处理机制:
    - 自动违规检测
    - 用户举报处理
    - 违规行为分级
    - 处罚措施执行
  
  申诉复议制度:
    - 用户申诉渠道
    - 申诉处理流程
    - 复议委员会制度
    - 申诉结果公示
  
  版主管理制度:
    - 版主选拔标准
    - 版主权限管理
    - 版主考核制度
    - 版主激励机制
```

#### 9.2.3 社区与业务集成

```yaml
图库业务集成:
  作品商业化:
    - 社区作品一键上架图库
    - 作品定价和授权管理
    - 销售数据反馈社区
    - 热门作品推广机制
  
  素材推荐:
    - 基于作品推荐相关素材
    - 教程中植入素材推荐
    - 用户创作需求匹配素材
    - 素材使用案例展示

商城业务集成:
  设备推荐:
    - 基于作品推荐适合设备
    - 设备使用教程关联
    - 用户设备使用反馈
    - 新设备体验分享
  
  耗材推荐:
    - 作品材料信息标注
    - 材料使用效果展示
    - 耗材购买便捷入口
    - 材料使用技巧分享

AI工具集成:
  AI功能展示:
    - AI处理效果作品展示
    - AI工具使用教程
    - AI创作过程分享
    - AI工具评测对比
  
  创作辅助:
    - 社区内容AI分析
    - 个性化内容推荐
    - 创作灵感AI生成
    - 作品质量AI评估

设备数据集成:
  使用数据分享:
    - 设备使用时长统计
    - 制作效率数据分析
    - 故障问题经验分享
    - 设备优化建议
  
  技术支持:
    - 设备问题社区求助
    - 厂商技术支持介入
    - 用户经验知识库
    - 设备使用最佳实践
```

## 🛒 10. 消费级打印设备耗材商城引入规划

### 10.1 商城系统架构设计

#### 10.1.1 商城业务模型

```plantuml
@startuml DTF_Mall_Business_Model

title DTF玩图平台商城业务模型 - 多角色商业生态

package "商城核心业务" {
  package "商品管理" {
    [设备商品] as DeviceProduct
    [耗材商品] as MaterialProduct
    [配件商品] as AccessoryProduct
    [软件服务] as SoftwareService
  }
  
  package "订单管理" {
    [订单创建] as OrderCreate
    [支付处理] as PaymentProcess
    [物流配送] as LogisticsDelivery
    [售后服务] as AfterSales
  }
  
  package "库存管理" {
    [库存监控] as InventoryMonitor
    [补货预警] as RestockAlert
    [供应链管理] as SupplyChain
    [仓储管理] as WarehouseManage
  }
}

package "商家角色体系" {
  actor [官方商城] as OfficialMall
  actor [设备厂商] as DeviceVendor
  actor [代理商] as Agent
  actor [第三方供应商] as ThirdPartySupplier
}

package "消费者体系" {
  actor [C端用户] as Consumer
  actor [企业用户] as Enterprise
  actor [教育机构] as Education
}

package "支付与物流" {
  [多币种支付] as MultiCurrencyPay
  [全球物流] as GlobalLogistics
  [本地化服务] as LocalService
}

' 商家权限关系
OfficialMall --> DeviceProduct : 官方设备销售
OfficialMall --> MaterialProduct : 官方耗材销售
DeviceVendor --> DeviceProduct : 自有设备销售
DeviceVendor --> MaterialProduct : 配套耗材销售
Agent --> DeviceProduct : 代理设备销售
Agent --> MaterialProduct : 代理耗材销售

' 消费者购买关系
Consumer --> DeviceProduct : 购买设备
Consumer --> MaterialProduct : 购买耗材
Enterprise --> DeviceProduct : 批量采购
Education --> DeviceProduct : 教育优惠

' 业务流程关系
DeviceProduct --> OrderCreate
MaterialProduct --> OrderCreate
OrderCreate --> PaymentProcess
PaymentProcess --> LogisticsDelivery
LogisticsDelivery --> AfterSales

' 支付物流关系
OrderCreate --> MultiCurrencyPay
PaymentProcess --> GlobalLogistics
GlobalLogistics --> LocalService

@enduml
```

#### 10.1.2 重点设备厂商产品规划

```yaml
印象派设备系列:
  产品定位: 消费级激光雕刻机领导品牌
  年销量目标: 1万台 → 10万台
  产品线规划:
    - 入门级: 印象派 Mini (价格区间: $299-$499)
    - 进阶级: 印象派 Pro (价格区间: $699-$999)
    - 专业级: 印象派 Master (价格区间: $1299-$1999)
  
  配套耗材:
    - 激光雕刻材料: 木材、亚克力、皮革、纸张
    - 切割材料: 薄木板、卡纸、布料
    - 保护材料: 蜂窝板、保护膜
    - 清洁用品: 清洁剂、清洁布、保养油
  
  技术特色:
    - 高精度激光头 (0.1mm精度)
    - 智能材料识别
    - 云端设计同步
    - 移动端控制

农产诺设备系列:
  产品定位: 农业应用激光设备专家
  年销量目标: 10万台级
  产品线规划:
    - 农用标识机: 果蔬标识、包装雕刻
    - 农具定制机: 农具个性化定制
    - 包装加工机: 农产品包装设计
  
  配套耗材:
    - 食品级材料: 可食用墨水、食品级标签
    - 包装材料: 环保纸张、生物降解材料
    - 农用标识: 防水标签、耐候材料
  
  技术特色:
    - 食品安全认证
    - 环保材料支持
    - 户外防护设计
    - 批量生产优化

日丰产设备系列:
  产品定位: 工业级精密加工设备
  年销量目标: 10万台级
  产品线规划:
    - 精密雕刻机: 金属、陶瓷精密加工
    - 工业切割机: 厚材料切割加工
    - 自动化产线: 批量生产解决方案
  
  配套耗材:
    - 金属材料: 不锈钢、铝合金、铜材
    - 陶瓷材料: 工业陶瓷、装饰陶瓷
    - 切割工具: 激光头、切割刀具
    - 冷却系统: 冷却液、过滤器
  
  技术特色:
    - 工业级精度控制
    - 自动化生产支持
    - 远程监控管理
    - 预测性维护
```

### 10.2 商城功能设计

#### 10.2.1 商品管理系统

```yaml
商品分类体系:
  设备分类:
    - 激光雕刻机: 入门级、进阶级、专业级、工业级
    - 3D打印机: FDM、SLA、SLS技术类型
    - 切割机: 激光切割、机械切割
    - 配套设备: 排烟系统、工作台、安全设备
  
  耗材分类:
    - 雕刻材料: 木材、亚克力、金属、皮革、纸张
    - 打印材料: PLA、ABS、PETG、树脂材料
    - 切割材料: 薄板材料、布料、薄膜
    - 辅助材料: 胶水、清洁剂、保护用品
  
  配件分类:
    - 激光头配件: 聚焦镜、保护镜、激光管
    - 机械配件: 皮带、导轨、电机、传感器
    - 电子配件: 控制板、电源、线缆
    - 安全配件: 防护眼镜、排烟管、急停开关

商品信息管理:
  基础信息:
    - 商品名称、型号、品牌
    - 商品描述、技术参数
    - 商品图片、视频演示
    - 使用说明、安装指南
  
  价格管理:
    - 多币种定价支持
    - 批量采购优惠
    - 会员价格体系
    - 促销活动价格
  
  库存管理:
    - 实时库存监控
    - 多仓库库存分配
    - 安全库存预警
    - 自动补货机制
  
  物流信息:
    - 商品重量尺寸
    - 包装规格要求
    - 运输限制说明
    - 配送时效承诺
```

#### 10.2.2 订单处理流程

```yaml
订单创建流程:
  购物车管理:
    - 商品添加购物车
    - 购物车商品编辑
    - 批量操作支持
    - 购物车数据同步
  
  订单确认:
    - 收货地址选择
    - 配送方式选择
    - 支付方式选择
    - 订单备注信息
  
  价格计算:
    - 商品价格计算
    - 运费计算
    - 税费计算
    - 优惠券折扣
    - 积分抵扣

支付处理系统:
  支付方式支持:
    - 信用卡支付 (Visa、MasterCard、AmEx)
    - 数字钱包 (PayPal、Apple Pay、Google Pay)
    - 银行转账 (ACH、Wire Transfer)
    - 分期付款 (Klarna、Affirm)
  
  多币种支持:
    - 美元 (USD) - 主要货币
    - 欧元 (EUR) - 欧洲市场
    - 英镑 (GBP) - 英国市场
    - 加元 (CAD) - 加拿大市场
    - 澳元 (AUD) - 澳洲市场
  
  支付安全:
    - PCI DSS合规
    - 3D Secure验证
    - 风险评估系统
    - 反欺诈检测

物流配送系统:
  配送方式:
    - 标准配送 (5-7个工作日)
    - 快速配送 (2-3个工作日)
    - 次日达配送 (限定区域)
    - 自提服务 (合作网点)
  
  物流合作伙伴:
    - 美国: FedEx、UPS、USPS
    - 欧洲: DHL、DPD、Hermes
    - 全球: DHL Express、FedEx International
  
  配送跟踪:
    - 实时物流跟踪
    - 配送状态推送
    - 签收确认
    - 配送异常处理
```

### 10.3 商城运营策略

#### 10.3.1 营销推广策略

```yaml
新用户获取:
  注册优惠:
    - 新用户注册送$50优惠券
    - 首单满$200减$30
    - 免费配送首单
    - 新手设备推荐包
  
  推荐奖励:
    - 推荐好友注册奖励$20积分
    - 好友首次购买推荐人获得$10
    - 推荐排行榜奖励
    - 推荐达人专属权益

用户留存策略:
  会员体系:
    - 铜牌会员 (消费$0-$500): 5%积分返还
    - 银牌会员 (消费$500-$2000): 8%积分返还
    - 金牌会员 (消费$2000-$5000): 10%积分返还
    - 钻石会员 (消费$5000+): 15%积分返还
  
  复购激励:
    - 耗材定期订购优惠
    - 设备保养提醒服务
    - 升级设备以旧换新
    - 生日专属优惠

促销活动策略:
  季节性促销:
    - 黑色星期五大促 (11月)
    - 网络星期一优惠 (11月)
    - 圣诞新年促销 (12月-1月)
    - 春季新品发布 (3月-4月)
  
  品类促销:
    - 设备新品预售优惠
    - 耗材批量购买折扣
    - 配件组合套装优惠
    - 清仓特价活动
```

#### 10.3.2 供应链管理

```yaml
供应商管理:
  官方供应商:
    - 印象派官方直供
    - 农产诺官方授权
    - 日丰产官方合作
    - 品质保证和售后支持
  
  第三方供应商:
    - 严格的供应商准入标准
    - 定期的质量审核
    - 供应商绩效评估
    - 供应商培训和支持
  
  采购策略:
    - 批量采购降低成本
    - 多供应商风险分散
    - 季节性采购计划
    - 新品引入评估

库存优化:
  需求预测:
    - 历史销售数据分析
    - 季节性趋势预测
    - 新品销量预估
    - 市场趋势分析
  
  库存策略:
    - ABC分类管理
    - 安全库存设置
    - 快慢周转分析
    - 库存周转率优化
  
  仓储管理:
    - 多地仓库布局
    - 智能仓储系统
    - 自动化拣货
    - 质量检验流程
```

## 🤖 11. AI助手引入规划

### 11.1 AI功能架构设计

#### 11.1.1 AI服务体系

```plantuml
@startuml DTF_AI_Service_Architecture

title DTF玩图平台AI服务架构 - 智能创作生态

package "AI核心服务" {
  package "图像处理AI" {
    [图生图] as ImageToImage
    [图像裂变] as ImageVariation
    [背景移除] as BackgroundRemoval
    [图像增强] as ImageEnhancement
    [风格转换] as StyleTransfer
  }
  
  package "3D建模AI" {
    [2.5D转换] as TwoPointFiveD
    [3D建模] as ThreeDModeling
    [模型优化] as ModelOptimization
    [STL生成] as STLGeneration
  }
  
  package "设计辅助AI" {
    [智能排版] as SmartLayout
    [色彩搭配] as ColorMatching
    [字体推荐] as FontRecommend
    [设计建议] as DesignSuggestion
  }
  
  package "内容生成AI" {
    [文本生成图像] as TextToImage
    [Logo生成] as LogoGeneration
    [图案生成] as PatternGeneration
    [创意灵感] as CreativeInspiration
  }
}

package "AI模型管理" {
  [模型版本控制] as ModelVersionControl
  [模型性能监控] as ModelPerformanceMonitor
  [模型A/B测试] as ModelABTest
  [模型更新部署] as ModelUpdateDeploy
}

package "AI服务集成" {
  [第三方AI服务] as ThirdPartyAI
  [自研AI模型] as InHouseAI
  [混合AI架构] as HybridAI
  [AI服务网关] as AIServiceGateway
}

package "用户交互界面" {
  [PC端AI工具] as PCTools
  [移动端AI工具] as MobileTools
  [Web端AI工具] as WebTools
  [API接口] as APIInterface
}

' AI服务关系
ImageToImage --> ModelVersionControl
ThreeDModeling --> ModelPerformanceMonitor
TextToImage --> ModelABTest

' 服务集成关系
ThirdPartyAI --> AIServiceGateway
InHouseAI --> AIServiceGateway
AIServiceGateway --> HybridAI

' 用户界面关系
PCTools --> AIServiceGateway
MobileTools --> AIServiceGateway
WebTools --> AIServiceGateway
APIInterface --> AIServiceGateway

@enduml
```

#### 11.1.2 AI功能详细规划

```yaml
图像处理AI功能:
  图生图 (Image-to-Image):
    - 基于Stable Diffusion模型
    - 支持多种艺术风格转换
    - 保持原图构图和主体
    - 可调节转换强度
    - 批量处理支持
  
  图像裂变 (Image Variation):
    - 基于原图生成多个变体
    - 保持主题一致性
    - 不同风格和色调变化
    - 创意灵感扩展
    - 设计方案对比
  
  背景移除 (Background Removal):
    - 高精度主体识别
    - 边缘细节保持
    - 透明背景输出
    - 批量处理支持
    - 手动微调功能
  
  图像增强 (Image Enhancement):
    - 智能降噪处理
    - 清晰度提升
    - 色彩饱和度优化
    - 对比度自动调节
    - HDR效果处理

3D建模AI功能:
  2.5D转换 (2.5D Conversion):
    - 平面图像立体化
    - 深度信息生成
    - 浮雕效果制作
    - 雕刻路径优化
    - 材料厚度建议
  
  3D建模 (3D Modeling):
    - 图像转3D模型
    - 多角度视图生成
    - 模型细节优化
    - 打印适配性检查
    - 支撑结构生成
  
  模型优化 (Model Optimization):
    - 网格简化处理
    - 文件大小压缩
    - 打印质量优化
    - 材料使用量计算
    - 打印时间预估

设计辅助AI功能:
  智能排版 (Smart Layout):
    - 自动版面设计
    - 元素对齐优化
    - 视觉平衡分析
    - 排版规则建议
    - 多方案生成
  
  色彩搭配 (Color Matching):
    - 智能配色方案
    - 色彩心理学应用
    - 品牌色彩识别
    - 无障碍设计支持
    - 色彩趋势分析
  
  字体推荐 (Font Recommendation):
    - 基于内容推荐字体
    - 字体情感分析
    - 可读性评估
    - 品牌一致性检查
    - 多语言字体支持

内容生成AI功能:
  文本生成图像 (Text-to-Image):
    - 基于DALL-E/Midjourney技术
    - 中英文提示词支持
    - 多种艺术风格
    - 高分辨率输出
    - 商业使用授权
  
  Logo生成 (Logo Generation):
    - 品牌名称输入生成
    - 行业特色识别
    - 多种设计风格
    - 矢量格式输出
    - 商标查重检查
  
  图案生成 (Pattern Generation):
    - 无缝拼接图案
    - 几何图案生成
    - 自然纹理模拟
    - 可调节复杂度
    - 多种输出格式
```

### 11.2 AI服务技术架构

#### 11.2.1 AI模型部署策略

```yaml
云端AI服务:
  AWS SageMaker部署:
    - 大型AI模型托管
    - 弹性扩缩容
    - 高可用性保障
    - 成本优化管理
  
  第三方AI API集成:
    - OpenAI GPT/DALL-E
    - Stability AI Stable Diffusion
    - Google Cloud AI
    - Azure Cognitive Services
  
  混合部署策略:
    - 核心模型自部署
    - 辅助功能API调用
    - 成本效益平衡
    - 数据安全保障

边缘AI计算:
  本地AI处理:
    - 轻量级模型本地部署
    - 离线处理能力
    - 隐私数据保护
    - 响应速度优化
  
  设备端AI:
    - 移动端AI模型
    - 实时预览功能
    - 低延迟处理
    - 带宽节省

AI服务网关:
  统一接口管理:
    - API版本控制
    - 请求路由分发
    - 负载均衡
    - 服务降级
  
  使用量控制:
    - 用户配额管理
    - 使用量统计
    - 成本控制
    - 滥用防护
```

#### 11.2.2 AI服务定价模型

```yaml
基础AI服务:
  免费额度:
    - 新用户每月50次免费AI处理
    - 基础图像处理功能
    - 标准质量输出
    - 社区版功能
  
  按次计费:
    - 图像处理: $0.10/次
    - 3D建模: $0.50/次
    - 文本生成图像: $0.20/次
    - 高级功能: $1.00/次

订阅服务:
  AI工具包订阅:
    - 基础版: $9.99/月 (500次处理)
    - 专业版: $29.99/月 (2000次处理)
    - 企业版: $99.99/月 (无限次处理)
    - 定制版: 按需定价
  
  增值服务:
    - 高分辨率输出: +$0.05/次
    - 批量处理: +$0.02/次
    - 优先处理: +$0.10/次
    - 商业授权: +$1.00/次

企业定制:
  私有化部署:
    - 独立AI模型部署
    - 定制化功能开发
    - 专属技术支持
    - 数据安全保障
  
  API授权:
    - 第三方集成授权
    - 白标解决方案
    - 技术培训支持
    - 持续更新维护
```

### 11.3 AI助手用户体验设计

#### 11.3.1 AI工具界面设计

```yaml
PC端AI工具:
  工具面板设计:
    - 左侧工具栏: AI功能分类
    - 中央画布: 实时预览区域
    - 右侧参数: 详细参数调节
    - 底部状态: 处理进度显示
  
  交互体验:
    - 拖拽式操作
    - 实时参数调节
    - 批量处理支持
    - 历史记录管理
  
  输出管理:
    - 多格式导出
    - 云端同步保存
    - 版本历史记录
    - 分享协作功能

移动端AI工具:
  简化界面设计:
    - 底部工具栏
    - 全屏预览模式
    - 手势操作支持
    - 快速分享功能
  
  移动优化:
    - 触摸友好界面
    - 离线处理能力
    - 低带宽优化
    - 电池使用优化

Web端AI工具:
  浏览器兼容:
    - 现代浏览器支持
    - WebGL加速
    - 渐进式Web应用
    - 跨平台一致性
  
  云端集成:
    - 无需安装使用
    - 实时协作编辑
    - 云端渲染加速
    - 多设备同步
```

#### 11.3.2 AI助手智能推荐

```yaml
个性化推荐:
  用户行为分析:
    - 使用习惯学习
    - 偏好风格识别
    - 创作主题分析
    - 技能水平评估
  
  智能建议:
    - 工具使用建议
    - 参数优化建议
    - 创作灵感推荐
    - 学习路径规划

创作辅助:
  智能提示:
    - 操作步骤指导
    - 最佳实践建议
    - 常见问题解答
    - 技巧窍门分享
  
  质量评估:
    - 作品质量评分
    - 改进建议提供
    - 技术问题识别
    - 优化方案推荐

学习成长:
  技能评估:
    - 当前技能水平
    - 学习进度跟踪
    - 成长路径规划
    - 目标设定建议
  
  教育内容:
    - 个性化教程推荐
    - 技能提升课程
    - 实战项目建议
    - 社区交流引导
```

## 🎯 12. 核心商业模式与定价策略

### 12.1 商业模式概览

#### 12.1.1 多层次收入模型

```yaml
核心收入来源:
  C端订阅服务:
    - 基础版: 免费 (限制功能)
    - 专业版: $9.99/月 (完整功能)
    - 创作者版: $19.99/月 (商业授权)
    - 企业版: $49.99/月 (团队协作)

  积分消费模式:
    - 图库下载: 5-50积分/张
    - AI修图: 10-100积分/次
    - 高级功能: 20-200积分/次
    - 商业授权: 100-1000积分/张

  设备厂商收入:
    - SaaS订阅: $99-999/月
    - 私有部署: $10,000-50,000/年
    - 技术服务: $500-2000/天
    - 代理分成: 10-30%

  平台交易抽成:
    - 图库销售: 30%平台抽成
    - 商城交易: 5-15%抽成
    - AI服务: 20%平台抽成
    - 第三方集成: 10-25%抽成
```

### 12.2 C端用户定价策略

#### 12.2.1 订阅套餐设计

```yaml
免费版 (Free):
  价格: $0/月
  功能限制:
    - 基础设计工具
    - 云存储: 500MB
    - 图库预览: 无限制
    - 图库下载: 5张/月 - 官方内部设计师、设备厂商私有图库（无IP侵权）
    - AI修图: 3次/月
    - 导出分辨率: 最大1080p
    - 水印: 带DTF水印
  
  初始赠送:
    - 50积分 (价值$0.5)
    - 新手教程完成额外20积分
    - 邀请好友每人10积分

专业版 (Pro):
  价格: $9.99/月 或 $99/年 (节省17%)
  功能包含:
    - 完整设计工具套件
    - 云存储: 10GB
    - 图库下载: 100张/月
    - AI修图: 50次/月
    - 导出分辨率: 最大4K
    - 无水印导出
    - 优先客服支持
    - 每月赠送200积分

创作者版 (Creator):
  价格: $19.99/月 或 $199/年 (节省17%)
  功能包含:
    - Pro版全部功能
    - 云存储: 50GB
    - 图库下载: 500张/月
    - AI修图: 200次/月
    - 商业授权使用
    - 图库上传销售 (70%分成)
    - 高级AI功能
    - 每月赠送500积分
    - 专属客服支持

企业版 (Enterprise):
  价格: $49.99/月 或 $499/年 (节省17%)
  功能包含:
    - Creator版全部功能
    - 云存储: 200GB
    - 无限图库下载
    - 无限AI修图
    - 团队协作功能
    - 品牌定制
    - API接口调用
    - 每月赠送1000积分
    - 专属客户经理
```

#### 12.2.2 积分消费定价

```yaml
积分充值套餐:
  基础套餐:
    - 500积分 = $5 (1积分=$0.01)
    - 1000积分 = $10 (无折扣)
    - 2000积分 = $20 (无折扣)
  
  优惠套餐:
    - 5000积分 = $45 (10%折扣)
    - 10000积分 = $80 (20%折扣)
    - 20000积分 = $140 (30%折扣)

图库内容定价:
  基础图片:
    - 标准分辨率: 5积分/张
    - 高清分辨率: 10积分/张
    - 超高清分辨率: 20积分/张
  
  高级图片:
    - AI生成图片: 15积分/张
    - 独家设计: 25积分/张
    - 商业授权: +50积分/张
  
  特殊内容:
    - 3D模型: 30-100积分/个
    - 动画模板: 50-200积分/个
    - 字体包: 20-80积分/套

AI修图服务定价:
  基础AI功能:
    - 背景移除: 10积分/次
    - 图像增强: 15积分/次
    - 色彩调整: 10积分/次
    - 尺寸调整: 5积分/次
  
  高级AI功能:
    - AI换脸: 50积分/次
    - 风格转换: 30积分/次
    - 智能修复: 40积分/次
    - 3D建模: 100积分/次
  
  专业AI功能:
    - 商业级处理: 2倍基础价格
    - 批量处理: 8折优惠
    - 高分辨率输出: +50%费用
    - 优先处理: +100%费用
```

### 12.3 与XTool差异化竞争策略

#### 12.3.1 XTool现状分析

```yaml
XTool劣势:
  技术架构:
    - 软件本地化，云服务有限
    - 生态封闭，仅服务自有设备
    - AI能力薄弱，缺乏智能化
    - 多端同步体验差
  
  商业模式:
    - 硬件销售为主，软件免费
    - 缺乏持续收入模式
    - 用户粘性低，复购率低
    - 生态价值挖掘不足
  
  用户体验:
    - 学习成本高
    - 社区生态薄弱
    - 内容资源有限
    - 缺乏协作功能

DTF差异化优势:
  技术领先:
    - 云原生架构，全端同步
    - 开放生态，支持多厂商
    - 强大AI能力，智能化设计
    - 跨平台一致体验
  
  商业创新:
    - 订阅+积分双重模式
    - 平台化运营，多方共赢
    - 数据驱动的个性化服务
    - 社区内容变现
  
  用户价值:
    - 零学习成本，即用即会
    - 丰富的内容生态
    - 强大的协作功能
    - 持续的功能更新
```

#### 12.3.2 市场抢占策略

```yaml
短期策略 (6-12个月):
  产品差异化:
    - 推出"一键设计"功能，降低使用门槛
    - 集成ChatGPT，提供AI设计助手
    - 建立丰富的模板库和素材库
    - 优化移动端体验，支持随时随地设计
  
  市场推广:
    - 免费版本吸引用户，培养使用习惯
    - 与设备厂商深度合作，预装软件
    - 社交媒体营销，展示设计作品
    - KOL合作，提升品牌知名度
  
  用户获取:
    - 新用户注册送50积分+7天Pro试用
    - 推荐奖励: 推荐成功获得100积分
    - 设备购买用户免费获得3个月Pro
    - 学生优惠: 50%折扣

中期策略 (1-2年):
  生态建设:
    - 建立设计师社区，UGC内容生产
    - 推出设计师认证计划，提升内容质量
    - 开放API，支持第三方开发者
    - 建立合作伙伴计划，扩大生态圈
  
  技术创新:
    - 推出AR/VR设计功能
    - 集成更多AI模型，提升智能化
    - 支持实时协作设计
    - 推出移动端专业版
  
  商业扩展:
    - 推出企业级解决方案
    - 开展B2B业务，服务设计公司
    - 拓展海外市场，本地化运营
    - 探索硬件合作，推出联名产品

长期策略 (2-5年):
  平台化发展:
    - 成为设计行业的"淘宝"
    - 建立完整的设计服务生态
    - 推出设计教育平台
    - 探索元宇宙设计应用
  
  技术领导:
    - 自研AI芯片，提升处理能力
    - 建立设计数据标准
    - 推动行业技术革新
    - 成为设计工具的技术标杆
```

### 12.4 收入预测与增长模型

#### 12.4.1 用户增长预测

```yaml
第一年目标:
  注册用户: 100,000
  付费用户: 5,000 (5%转化率)
  月活跃用户: 30,000
  用户留存率: 60% (月留存)

收入构成:
  订阅收入: $50,000/月
  积分消费: $20,000/月
  平台抽成: $10,000/月
  企业服务: $30,000/月
  总计: $110,000/月

第三年目标:
  注册用户: 1,000,000
  付费用户: 100,000 (10%转化率)
  月活跃用户: 500,000
  用户留存率: 80% (月留存)

收入构成:
  订阅收入: $1,000,000/月
  积分消费: $500,000/月
  平台抽成: $300,000/月
  企业服务: $700,000/月
  总计: $2,500,000/月
```

#### 12.4.2 关键成功指标

```yaml
用户指标:
  - 用户获取成本 (CAC): < $20
  - 用户生命周期价值 (LTV): > $200
  - LTV/CAC比例: > 10:1
  - 月流失率: < 5%
  - 净推荐值 (NPS): > 50

业务指标:
  - 月经常性收入 (MRR) 增长率: > 20%
  - 年度经常性收入 (ARR): 目标$30M
  - 毛利率: > 80%
  - 付费转化率: > 10%
  - 平均每用户收入 (ARPU): > $15/月

产品指标:
  - 日活跃用户 (DAU): 目标200,000
  - 用户参与度: 平均使用时长 > 30分钟
  - 功能使用率: 核心功能使用率 > 70%
  - 客户满意度: > 4.5/5.0
  - 产品市场契合度 (PMF): 40%用户认为"非常失望"如果失去产品
```

### 12.5 定价策略优化

#### 12.5.1 动态定价机制

```yaml
地区定价策略:
  发达市场 (美国/欧洲):
    - 标准定价
    - 高价值功能优先
    - 企业级服务重点
  
  新兴市场 (东南亚/拉美):
    - 30-50%折扣
    - 本地化支付方式
    - 基础功能优先
  
  学生市场:
    - 50%教育折扣
    - 校园推广计划
    - 毕业生转换优惠

季节性定价:
  - 黑色星期五: 年费50%折扣
  - 新年促销: 买一年送三个月
  - 学期开始: 学生专享优惠
  - 节日活动: 限时功能免费
```

#### 12.5.2 用户分层策略

```yaml
新用户培养:
  - 免费试用期: 14天Pro功能
  - 新手任务: 完成获得积分奖励
  - 个性化推荐: AI推荐适合的套餐
  - 客服引导: 专人协助功能使用

活跃用户维护:
  - 忠诚度计划: 连续订阅享受折扣
  - 功能预览: 提前体验新功能
  - 社区特权: VIP用户专属社区
  - 生日优惠: 个性化生日礼品

流失用户挽回:
  - 暂停订阅: 3个月暂停选项
  - 降级优惠: 提供更便宜的套餐
  - 重新激活: 特价重新订阅优惠
  - 问卷调研: 了解流失原因并改进
```

这样的商业模式设计既考虑了用户的实际需求，又建立了可持续的收入模型，同时通过差异化竞争策略确保在市场中的领先地位。

**核心亮点**:

1. **社区生态**: 多角色互动、内容商业化、业务深度集成
2. **商城体系**: 三大设备厂商产品线、全球化运营、供应链优化
3. **AI智能**: 全栈AI服务、混合部署、个性化体验
